@echo off
chcp 65001 >nul
title مؤسسة وقود المستقبل - Future Fuel Corporation

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    مؤسسة وقود المستقبل                      ║
echo ║                Future Fuel Corporation                       ║
echo ║                      الإصدار 2.2.0                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 تشغيل التطبيق...
echo 🚀 Starting Application...
echo.

REM التحقق من وجود Node.js
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت
    echo ❌ Error: Node.js is not installed
    echo.
    echo يرجى تثبيت Node.js من: https://nodejs.org
    echo Please install Node.js from: https://nodejs.org
    pause
    exit /b 1
)

REM التحقق من وجود node_modules
if not exist "node_modules" (
    echo ⚠️  تحذير: مجلد node_modules غير موجود
    echo ⚠️  Warning: node_modules folder not found
    echo.
    echo 📦 تثبيت التبعيات...
    echo 📦 Installing dependencies...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
    echo ✅ Dependencies installed successfully
    echo.
)

REM التحقق من وجود Electron
if not exist "node_modules\electron" (
    echo 📦 تثبيت Electron...
    echo 📦 Installing Electron...
    call npm install electron --save-dev
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت Electron
        echo ❌ Failed to install Electron
        pause
        exit /b 1
    )
)

echo 🎯 تشغيل تطبيق Electron...
echo 🎯 Launching Electron app...
echo.

REM تشغيل التطبيق
call npm start

if %errorlevel% neq 0 (
    echo.
    echo ❌ فشل في تشغيل التطبيق
    echo ❌ Failed to start application
    echo.
    echo 🔧 محاولة تشغيل مباشر...
    echo 🔧 Trying direct launch...
    node_modules\.bin\electron.cmd main.js
)

echo.
echo 👋 تم إغلاق التطبيق
echo 👋 Application closed
echo.
pause
