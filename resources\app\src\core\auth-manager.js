// نظام المصادقة والترخيص المركزي
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.currentLicense = null;
        this.isAuthenticated = false;
        this.sessionTimeout = 30 * 60 * 1000; // 30 دقيقة
        this.maxLoginAttempts = 3;
        this.lockoutDuration = 15 * 60 * 1000; // 15 دقيقة
        this.sessionTimer = null;
        
        this.validCredentials = [
            { username: 'admin', password: 'admin123', role: 'admin', name: 'المدير العام' },
            { username: 'manager', password: 'manager123', role: 'manager', name: 'المدير' },
            { username: 'user', password: 'user123', role: 'user', name: 'المستخدم' },
            { username: 'employee', password: 'employee123', role: 'employee', name: 'الموظف' }
        ];

        this.validLicenseKeys = {
            'FF-DEMO-2024-TEST': { type: 'demo', duration: 30, features: ['basic'] },
            'FF-BASIC-2024-001': { type: 'basic', duration: 365, features: ['basic', 'customers'] },
            'FF-STANDARD-2024-002': { type: 'standard', duration: 365, features: ['basic', 'customers', 'reports'] },
            'FF-PREMIUM-2024-PRO': { type: 'premium', duration: 730, features: ['all'] },
            'FF-ENTERPRISE-2024-ENT': { type: 'enterprise', duration: 1095, features: ['all', 'multi-user'] }
        };

        this.init();
    }

    // تهيئة النظام
    init() {
        this.checkExistingSession();
        this.setupEventListeners();
        this.startSessionMonitoring();
    }

    // فحص الجلسة الموجودة
    checkExistingSession() {
        try {
            const sessionData = localStorage.getItem('userSession');
            const licenseData = localStorage.getItem('appLicense');

            if (sessionData && licenseData) {
                const session = JSON.parse(sessionData);
                const license = JSON.parse(licenseData);

                // فحص صلاحية الجلسة
                if (session.expiryDate && new Date(session.expiryDate) > new Date()) {
                    // فحص صلاحية الترخيص
                    if (license.expiryDate && new Date(license.expiryDate) > new Date()) {
                        this.currentUser = session;
                        this.currentLicense = license;
                        this.isAuthenticated = true;
                        this.startSessionTimer();
                        console.log('✅ تم استعادة الجلسة الموجودة');
                        return true;
                    }
                }
            }

            // مسح البيانات المنتهية الصلاحية
            this.clearSession();
            return false;

        } catch (error) {
            console.error('❌ خطأ في فحص الجلسة:', error);
            this.clearSession();
            return false;
        }
    }

    // تسجيل الدخول
    async login(username, password, rememberMe = false) {
        try {
            console.log('🔑 محاولة تسجيل الدخول...');

            // فحص محاولات تسجيل الدخول
            if (this.isAccountLocked(username)) {
                throw new Error('الحساب مقفل مؤقتاً. يرجى المحاولة لاحقاً.');
            }

            // التحقق من بيانات الاعتماد
            const user = this.validateCredentials(username, password);
            if (!user) {
                this.recordFailedAttempt(username);
                throw new Error('اسم المستخدم أو كلمة المرور غير صحيحة');
            }

            // فحص الترخيص
            const licenseValid = await this.validateCurrentLicense();
            if (!licenseValid) {
                throw new Error('الترخيص غير صالح أو منتهي الصلاحية');
            }

            // إنشاء الجلسة
            await this.createSession(user, rememberMe);

            // مسح محاولات تسجيل الدخول الفاشلة
            this.clearFailedAttempts(username);

            console.log('✅ تم تسجيل الدخول بنجاح');
            this.emit('login', { user: this.currentUser });

            return {
                success: true,
                user: this.currentUser,
                license: this.currentLicense
            };

        } catch (error) {
            console.error('❌ خطأ في تسجيل الدخول:', error);
            throw error;
        }
    }

    // التحقق من بيانات الاعتماد
    validateCredentials(username, password) {
        return this.validCredentials.find(cred => 
            cred.username === username && cred.password === password
        );
    }

    // فحص الترخيص الحالي
    async validateCurrentLicense() {
        try {
            const licenseData = localStorage.getItem('appLicense');
            if (!licenseData) {
                return false;
            }

            const license = JSON.parse(licenseData);
            
            // فحص انتهاء الصلاحية
            if (license.expiryDate && new Date(license.expiryDate) <= new Date()) {
                return false;
            }

            // فحص صحة المفتاح
            if (!this.validLicenseKeys[license.key]) {
                return false;
            }

            this.currentLicense = license;
            return true;

        } catch (error) {
            console.error('❌ خطأ في فحص الترخيص:', error);
            return false;
        }
    }

    // تفعيل ترخيص جديد
    async activateLicense(licenseKey, clientInfo) {
        try {
            console.log('🔑 تفعيل الترخيص...', licenseKey);

            // التحقق من صحة المفتاح
            const keyData = this.validLicenseKeys[licenseKey];
            if (!keyData) {
                throw new Error('مفتاح الترخيص غير صحيح');
            }

            // إنشاء بيانات الترخيص
            const deviceId = this.generateDeviceId();
            const now = new Date();
            const expiryDate = new Date(now.getTime() + (keyData.duration * 24 * 60 * 60 * 1000));

            const licenseData = {
                key: licenseKey,
                type: keyData.type,
                deviceId: deviceId,
                activationDate: now.toISOString(),
                expiryDate: expiryDate.toISOString(),
                status: 'active',
                features: keyData.features,
                clientInfo: clientInfo || {},
                signature: this.generateLicenseSignature(licenseKey, deviceId)
            };

            // حفظ الترخيص
            localStorage.setItem('appLicense', JSON.stringify(licenseData));
            this.currentLicense = licenseData;

            console.log('✅ تم تفعيل الترخيص بنجاح');
            this.emit('license-activated', { license: licenseData });

            return licenseData;

        } catch (error) {
            console.error('❌ خطأ في تفعيل الترخيص:', error);
            throw error;
        }
    }

    // إنشاء الجلسة
    async createSession(user, rememberMe) {
        const sessionDuration = rememberMe ? 7 * 24 * 60 * 60 * 1000 : this.sessionTimeout;
        
        const sessionData = {
            username: user.username,
            name: user.name,
            role: user.role,
            loginTime: new Date().toISOString(),
            expiryDate: new Date(Date.now() + sessionDuration).toISOString(),
            deviceId: this.generateDeviceId(),
            rememberMe: rememberMe
        };

        // حفظ الجلسة
        localStorage.setItem('userSession', JSON.stringify(sessionData));
        this.currentUser = sessionData;
        this.isAuthenticated = true;

        // بدء مؤقت الجلسة
        this.startSessionTimer();

        // حفظ في قاعدة البيانات إذا كانت متاحة
        if (window.DataManager) {
            await window.DataManager.add('sessions', {
                ...sessionData,
                id: sessionData.deviceId
            });
        }
    }

    // تسجيل الخروج
    async logout() {
        try {
            console.log('🚪 تسجيل الخروج...');

            // إيقاف مؤقت الجلسة
            if (this.sessionTimer) {
                clearTimeout(this.sessionTimer);
                this.sessionTimer = null;
            }

            // مسح البيانات
            this.clearSession();

            console.log('✅ تم تسجيل الخروج بنجاح');
            this.emit('logout');

            // إعادة توجيه لصفحة تسجيل الدخول
            if (window.AppRouter) {
                window.AppRouter.navigate('/login');
            } else {
                window.location.href = 'src/auth/login.html';
            }

        } catch (error) {
            console.error('❌ خطأ في تسجيل الخروج:', error);
        }
    }

    // مسح الجلسة
    clearSession() {
        localStorage.removeItem('userSession');
        this.currentUser = null;
        this.isAuthenticated = false;
        
        if (this.sessionTimer) {
            clearTimeout(this.sessionTimer);
            this.sessionTimer = null;
        }
    }

    // بدء مراقبة الجلسة
    startSessionMonitoring() {
        // فحص الجلسة كل دقيقة
        setInterval(() => {
            if (this.isAuthenticated && this.currentUser) {
                const expiryDate = new Date(this.currentUser.expiryDate);
                if (expiryDate <= new Date()) {
                    console.log('⏰ انتهت صلاحية الجلسة');
                    this.logout();
                }
            }
        }, 60000);
    }

    // بدء مؤقت الجلسة
    startSessionTimer() {
        if (this.sessionTimer) {
            clearTimeout(this.sessionTimer);
        }

        if (this.currentUser && !this.currentUser.rememberMe) {
            const timeUntilExpiry = new Date(this.currentUser.expiryDate) - new Date();
            
            this.sessionTimer = setTimeout(() => {
                console.log('⏰ انتهت مدة الجلسة');
                this.logout();
            }, timeUntilExpiry);
        }
    }

    // تجديد الجلسة
    renewSession() {
        if (this.isAuthenticated && this.currentUser) {
            const newExpiryDate = new Date(Date.now() + this.sessionTimeout);
            this.currentUser.expiryDate = newExpiryDate.toISOString();
            localStorage.setItem('userSession', JSON.stringify(this.currentUser));
            this.startSessionTimer();
        }
    }

    // فحص الصلاحيات
    hasPermission(permission) {
        if (!this.isAuthenticated || !this.currentUser) {
            return false;
        }

        const rolePermissions = {
            admin: ['all'],
            manager: ['read', 'write', 'reports', 'customers', 'licenses'],
            user: ['read', 'write', 'customers'],
            employee: ['read', 'customers']
        };

        const userPermissions = rolePermissions[this.currentUser.role] || [];
        return userPermissions.includes('all') || userPermissions.includes(permission);
    }

    // فحص ميزة الترخيص
    hasFeature(feature) {
        if (!this.currentLicense || !this.currentLicense.features) {
            return false;
        }

        return this.currentLicense.features.includes('all') || 
               this.currentLicense.features.includes(feature);
    }

    // تسجيل محاولة فاشلة
    recordFailedAttempt(username) {
        const attempts = JSON.parse(localStorage.getItem('loginAttempts') || '{}');
        const now = Date.now();
        
        if (!attempts[username]) {
            attempts[username] = { count: 0, lastAttempt: now };
        }
        
        attempts[username].count++;
        attempts[username].lastAttempt = now;
        
        localStorage.setItem('loginAttempts', JSON.stringify(attempts));
    }

    // مسح محاولات فاشلة
    clearFailedAttempts(username) {
        const attempts = JSON.parse(localStorage.getItem('loginAttempts') || '{}');
        delete attempts[username];
        localStorage.setItem('loginAttempts', JSON.stringify(attempts));
    }

    // فحص قفل الحساب
    isAccountLocked(username) {
        const attempts = JSON.parse(localStorage.getItem('loginAttempts') || '{}');
        const userAttempts = attempts[username];
        
        if (!userAttempts) return false;
        
        const timeSinceLastAttempt = Date.now() - userAttempts.lastAttempt;
        
        if (userAttempts.count >= this.maxLoginAttempts) {
            return timeSinceLastAttempt < this.lockoutDuration;
        }
        
        return false;
    }

    // إنشاء معرف الجهاز
    generateDeviceId() {
        let deviceId = localStorage.getItem('deviceId');
        if (!deviceId) {
            deviceId = 'DEV-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('deviceId', deviceId);
        }
        return deviceId;
    }

    // إنشاء توقيع الترخيص
    generateLicenseSignature(licenseKey, deviceId) {
        const data = licenseKey + deviceId + Date.now();
        return btoa(data).substr(0, 16);
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // تجديد الجلسة عند النشاط
        ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
            document.addEventListener(event, () => {
                if (this.isAuthenticated) {
                    this.renewSession();
                }
            }, { passive: true });
        });
    }

    // إطلاق الأحداث
    emit(eventName, data = {}) {
        const event = new CustomEvent(`auth:${eventName}`, { detail: data });
        window.dispatchEvent(event);
    }

    // الحصول على معلومات المستخدم الحالي
    getCurrentUser() {
        return this.currentUser;
    }

    // الحصول على معلومات الترخيص الحالي
    getCurrentLicense() {
        return this.currentLicense;
    }

    // فحص حالة المصادقة
    isLoggedIn() {
        return this.isAuthenticated;
    }
}

// إنشاء مثيل عام لمدير المصادقة
window.AuthManager = new AuthManager();

// تصدير للاستخدام في Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}
