<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام طلبات التفعيل المحسن</title>
    <link rel="stylesheet" href="admin-panel.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .test-content {
            padding: 30px;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        
        .test-section h3 {
            color: #495057;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .test-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .test-btn.primary {
            background: #007bff;
            color: white;
        }
        
        .test-btn.success {
            background: #28a745;
            color: white;
        }
        
        .test-btn.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .test-btn.danger {
            background: #dc3545;
            color: white;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .test-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .demo-requests {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .demo-request {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }
        
        .demo-request h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        
        .demo-request p {
            margin: 5px 0;
            font-size: 0.9rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-flask"></i> اختبار نظام طلبات التفعيل المحسن</h1>
            <p>أداة لاختبار جميع ميزات نظام طلبات التفعيل الجديد</p>
        </div>
        
        <div class="test-content">
            <!-- قسم إنشاء طلبات تجريبية -->
            <div class="test-section">
                <h3><i class="fas fa-plus-circle"></i> إنشاء طلبات تجريبية</h3>
                <div class="test-buttons">
                    <button class="test-btn primary" onclick="createSingleRequest()">
                        <i class="fas fa-user-plus"></i> إنشاء طلب واحد
                    </button>
                    <button class="test-btn success" onclick="createMultipleRequests()">
                        <i class="fas fa-users"></i> إنشاء 5 طلبات
                    </button>
                    <button class="test-btn warning" onclick="createUrgentRequest()">
                        <i class="fas fa-exclamation-triangle"></i> طلب عاجل
                    </button>
                </div>
                <div class="test-result" id="create-result"></div>
            </div>
            
            <!-- قسم اختبار الفلاتر -->
            <div class="test-section">
                <h3><i class="fas fa-filter"></i> اختبار نظام التصفية</h3>
                <div class="test-buttons">
                    <button class="test-btn primary" onclick="testSearchFilter()">
                        <i class="fas fa-search"></i> اختبار البحث
                    </button>
                    <button class="test-btn primary" onclick="testStatusFilter()">
                        <i class="fas fa-list"></i> اختبار فلتر الحالة
                    </button>
                    <button class="test-btn primary" onclick="testStateFilter()">
                        <i class="fas fa-map-marker-alt"></i> اختبار فلتر الولاية
                    </button>
                </div>
                <div class="test-result" id="filter-result"></div>
            </div>
            
            <!-- قسم اختبار الإشعارات -->
            <div class="test-section">
                <h3><i class="fas fa-bell"></i> اختبار نظام الإشعارات</h3>
                <div class="test-buttons">
                    <button class="test-btn success" onclick="testNotificationSound()">
                        <i class="fas fa-volume-up"></i> اختبار الصوت
                    </button>
                    <button class="test-btn warning" onclick="testVisualNotification()">
                        <i class="fas fa-eye"></i> اختبار التأثير البصري
                    </button>
                    <button class="test-btn danger" onclick="simulateNewRequest()">
                        <i class="fas fa-plus"></i> محاكاة طلب جديد
                    </button>
                </div>
                <div class="test-result" id="notification-result"></div>
            </div>
            
            <!-- قسم إدارة البيانات -->
            <div class="test-section">
                <h3><i class="fas fa-database"></i> إدارة البيانات</h3>
                <div class="test-buttons">
                    <button class="test-btn primary" onclick="viewStoredData()">
                        <i class="fas fa-eye"></i> عرض البيانات المحفوظة
                    </button>
                    <button class="test-btn warning" onclick="clearAllData()">
                        <i class="fas fa-trash"></i> مسح جميع البيانات
                    </button>
                    <button class="test-btn success" onclick="exportData()">
                        <i class="fas fa-download"></i> تصدير البيانات
                    </button>
                </div>
                <div class="test-result" id="data-result"></div>
            </div>
            
            <!-- قسم الطلبات التجريبية -->
            <div class="test-section">
                <h3><i class="fas fa-list"></i> الطلبات التجريبية المتاحة</h3>
                <div class="demo-requests" id="demo-requests">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </div>
            </div>
            
            <!-- أزرار التنقل -->
            <div class="test-section">
                <h3><i class="fas fa-external-link-alt"></i> التنقل</h3>
                <div class="test-buttons">
                    <button class="test-btn primary" onclick="openAdminPanel()">
                        <i class="fas fa-tachometer-alt"></i> فتح لوحة التحكم
                    </button>
                    <button class="test-btn success" onclick="openLoginPage()">
                        <i class="fas fa-sign-in-alt"></i> صفحة تسجيل الدخول
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات تجريبية للطلبات
        const demoRequestsData = [
            {
                firstName: 'أحمد',
                lastName: 'محمد',
                phone: '0555123456',
                state: 'الجزائر',
                municipality: 'الجزائر الوسطى',
                businessName: 'محطة الأمل',
                notes: 'طلب عادي للتفعيل'
            },
            {
                firstName: 'فاطمة',
                lastName: 'بن علي',
                phone: '0666789012',
                state: 'وهران',
                municipality: 'وهران',
                businessName: 'محطة النور',
                notes: 'طلب سريع'
            },
            {
                firstName: 'يوسف',
                lastName: 'مرادي',
                phone: '0777345678',
                state: 'قسنطينة',
                municipality: 'قسنطينة',
                businessName: 'محطة المستقبل',
                notes: 'طلب عاجل'
            }
        ];

        // عرض الطلبات التجريبية
        function displayDemoRequests() {
            const container = document.getElementById('demo-requests');
            container.innerHTML = demoRequestsData.map((req, index) => `
                <div class="demo-request">
                    <h4>${req.firstName} ${req.lastName}</h4>
                    <p><strong>الهاتف:</strong> ${req.phone}</p>
                    <p><strong>الموقع:</strong> ${req.state} - ${req.municipality}</p>
                    <p><strong>المؤسسة:</strong> ${req.businessName}</p>
                    <p><strong>ملاحظات:</strong> ${req.notes}</p>
                    <button class="test-btn primary" onclick="createSpecificRequest(${index})">
                        <i class="fas fa-plus"></i> إنشاء هذا الطلب
                    </button>
                </div>
            `).join('');
        }

        // دوال الاختبار
        function createSingleRequest() {
            const randomReq = demoRequestsData[Math.floor(Math.random() * demoRequestsData.length)];
            const request = {
                ...randomReq,
                id: Date.now(),
                status: 'pending',
                requestDate: new Date().toISOString()
            };
            
            saveRequestToStorage(request);
            updateResult('create-result', `تم إنشاء طلب جديد:\n${JSON.stringify(request, null, 2)}`);
        }

        function createMultipleRequests() {
            const requests = [];
            for (let i = 0; i < 5; i++) {
                const randomReq = demoRequestsData[Math.floor(Math.random() * demoRequestsData.length)];
                const request = {
                    ...randomReq,
                    id: Date.now() + i,
                    status: 'pending',
                    requestDate: new Date().toISOString(),
                    firstName: randomReq.firstName + ' ' + (i + 1)
                };
                requests.push(request);
                saveRequestToStorage(request);
            }
            updateResult('create-result', `تم إنشاء 5 طلبات جديدة:\n${requests.map(r => r.firstName + ' ' + r.lastName).join('\n')}`);
        }

        function createUrgentRequest() {
            const request = {
                ...demoRequestsData[2],
                id: Date.now(),
                status: 'pending',
                requestDate: new Date().toISOString(),
                notes: 'طلب عاجل - يحتاج معالجة فورية',
                urgent: true
            };
            
            saveRequestToStorage(request);
            updateResult('create-result', `تم إنشاء طلب عاجل:\n${JSON.stringify(request, null, 2)}`);
        }

        function createSpecificRequest(index) {
            const request = {
                ...demoRequestsData[index],
                id: Date.now(),
                status: 'pending',
                requestDate: new Date().toISOString()
            };
            
            saveRequestToStorage(request);
            updateResult('create-result', `تم إنشاء الطلب المحدد:\n${request.firstName} ${request.lastName}`);
        }

        function saveRequestToStorage(request) {
            const stored = JSON.parse(localStorage.getItem('activationRequests') || '[]');
            stored.push(request);
            localStorage.setItem('activationRequests', JSON.stringify(stored));
        }

        function testSearchFilter() {
            updateResult('filter-result', 'اختبار البحث:\n- ابحث عن "أحمد"\n- ابحث عن رقم هاتف\n- ابحث عن اسم مؤسسة');
        }

        function testStatusFilter() {
            updateResult('filter-result', 'اختبار فلتر الحالة:\n- فلتر "في الانتظار"\n- فلتر "تم الموافقة"\n- فلتر "مرفوض"');
        }

        function testStateFilter() {
            updateResult('filter-result', 'اختبار فلتر الولاية:\n- فلتر "الجزائر"\n- فلتر "وهران"\n- فلتر "قسنطينة"');
        }

        function testNotificationSound() {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
                
                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.3);
                
                updateResult('notification-result', 'تم تشغيل صوت الإشعار بنجاح!');
            } catch (error) {
                updateResult('notification-result', 'خطأ في تشغيل الصوت: ' + error.message);
            }
        }

        function testVisualNotification() {
            document.body.style.animation = 'pulse 2s infinite';
            setTimeout(() => {
                document.body.style.animation = '';
            }, 4000);
            updateResult('notification-result', 'تم تطبيق التأثير البصري لمدة 4 ثوان');
        }

        function simulateNewRequest() {
            createSingleRequest();
            updateResult('notification-result', 'تم محاكاة طلب جديد - تحقق من لوحة التحكم');
        }

        function viewStoredData() {
            const stored = localStorage.getItem('activationRequests');
            if (stored) {
                const requests = JSON.parse(stored);
                updateResult('data-result', `عدد الطلبات المحفوظة: ${requests.length}\n\n${JSON.stringify(requests, null, 2)}`);
            } else {
                updateResult('data-result', 'لا توجد بيانات محفوظة');
            }
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                localStorage.removeItem('activationRequests');
                localStorage.removeItem('adminLicenses');
                localStorage.removeItem('adminClients');
                updateResult('data-result', 'تم مسح جميع البيانات');
            }
        }

        function exportData() {
            const data = {
                activationRequests: JSON.parse(localStorage.getItem('activationRequests') || '[]'),
                licenses: JSON.parse(localStorage.getItem('adminLicenses') || '[]'),
                clients: JSON.parse(localStorage.getItem('adminClients') || '[]')
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'activation-requests-data.json';
            a.click();
            URL.revokeObjectURL(url);
            
            updateResult('data-result', 'تم تصدير البيانات كملف JSON');
        }

        function openAdminPanel() {
            window.open('admin-panel.html', '_blank');
        }

        function openLoginPage() {
            window.open('../auth/login.html', '_blank');
        }

        function updateResult(elementId, text) {
            document.getElementById(elementId).textContent = text;
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            displayDemoRequests();
        });
    </script>
</body>
</html>
