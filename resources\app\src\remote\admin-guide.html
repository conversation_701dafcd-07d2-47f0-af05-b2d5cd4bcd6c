<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل استخدام لوحة التحكم - CFGPLProgram</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .guide-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .guide-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .guide-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s ease;
            cursor: pointer;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-card.blue {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .feature-card.green {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .feature-card.purple {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .feature-card.orange {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }
        
        .step-list {
            list-style: none;
            padding: 0;
        }
        
        .step-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 5px solid #007bff;
            transition: all 0.3s ease;
        }
        
        .step-item:hover {
            transform: translateX(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .step-number {
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 15px;
        }
        
        .quick-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .quick-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        
        .quick-link.success {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .quick-link.warning {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }
        
        .quick-link.danger {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .tip-box {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #fdcb6e;
        }
        
        .warning-box {
            background: linear-gradient(135deg, #ff7675 0%, #fd79a8 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #e84393;
        }
        
        .success-box {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #00b894;
        }
        
        .video-placeholder {
            background: #2d3436;
            color: white;
            padding: 60px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
        }
        
        .accordion {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            margin: 10px 0;
            overflow: hidden;
        }
        
        .accordion-header {
            background: #f8f9fa;
            padding: 15px 20px;
            cursor: pointer;
            border-bottom: 1px solid #dee2e6;
            transition: background 0.3s ease;
        }
        
        .accordion-header:hover {
            background: #e9ecef;
        }
        
        .accordion-content {
            padding: 20px;
            display: none;
        }
        
        .accordion.active .accordion-content {
            display: block;
        }
    </style>
</head>
<body>
    <div class="guide-container">
        <!-- رأس الدليل -->
        <div class="guide-header">
            <h1 style="color: #333; margin-bottom: 20px;">
                <i class="fas fa-book-open"></i>
                دليل استخدام لوحة التحكم
            </h1>
            <p style="font-size: 1.2rem; color: #666; margin-bottom: 30px;">
                تعلم كيفية استخدام جميع ميزات لوحة التحكم بفعالية
            </p>
            
            <div style="margin-top: 30px;">
                <a href="admin-panel-fixed.html" class="quick-link success">
                    <i class="fas fa-rocket"></i> فتح لوحة التحكم
                </a>
                <a href="test-admin-panel.html" class="quick-link warning">
                    <i class="fas fa-vial"></i> صفحة الاختبار
                </a>
                <a href="debug-admin-panel.html" class="quick-link danger">
                    <i class="fas fa-bug"></i> أداة التشخيص
                </a>
            </div>
        </div>
        
        <!-- نظرة عامة على الميزات -->
        <div class="guide-section">
            <h2><i class="fas fa-star"></i> الميزات الرئيسية</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <i class="fas fa-tachometer-alt" style="font-size: 2rem; margin-bottom: 15px;"></i>
                    <h3>لوحة المعلومات</h3>
                    <p>عرض شامل للإحصائيات والأنشطة الحديثة</p>
                </div>
                
                <div class="feature-card blue">
                    <i class="fas fa-key" style="font-size: 2rem; margin-bottom: 15px;"></i>
                    <h3>إدارة التراخيص</h3>
                    <p>إنشاء وإدارة ومراقبة جميع التراخيص</p>
                </div>
                
                <div class="feature-card green">
                    <i class="fas fa-users" style="font-size: 2rem; margin-bottom: 15px;"></i>
                    <h3>إدارة العملاء</h3>
                    <p>قاعدة بيانات شاملة لجميع العملاء</p>
                </div>
                
                <div class="feature-card purple">
                    <i class="fas fa-clipboard-list" style="font-size: 2rem; margin-bottom: 15px;"></i>
                    <h3>طلبات التفعيل</h3>
                    <p>مراجعة والموافقة على طلبات التفعيل</p>
                </div>
                
                <div class="feature-card orange">
                    <i class="fas fa-chart-line" style="font-size: 2rem; margin-bottom: 15px;"></i>
                    <h3>المراقبة</h3>
                    <p>تحليلات مفصلة وتقارير الأداء</p>
                </div>
                
                <div class="feature-card">
                    <i class="fas fa-cog" style="font-size: 2rem; margin-bottom: 15px;"></i>
                    <h3>الإعدادات</h3>
                    <p>تخصيص النظام والأمان</p>
                </div>
            </div>
        </div>
        
        <!-- دليل البدء السريع -->
        <div class="guide-section">
            <h2><i class="fas fa-play-circle"></i> البدء السريع</h2>
            
            <ol class="step-list">
                <li class="step-item">
                    <span class="step-number">1</span>
                    <strong>تسجيل الدخول:</strong>
                    استخدم بيانات الاعتماد الإدارية للوصول إلى لوحة التحكم
                </li>
                
                <li class="step-item">
                    <span class="step-number">2</span>
                    <strong>استكشاف لوحة المعلومات:</strong>
                    راجع الإحصائيات السريعة والأنشطة الحديثة
                </li>
                
                <li class="step-item">
                    <span class="step-number">3</span>
                    <strong>إدارة التراخيص:</strong>
                    انتقل إلى قسم التراخيص لإنشاء أو تعديل التراخيص
                </li>
                
                <li class="step-item">
                    <span class="step-number">4</span>
                    <strong>مراجعة العملاء:</strong>
                    تحقق من قائمة العملاء وحالة تراخيصهم
                </li>
                
                <li class="step-item">
                    <span class="step-number">5</span>
                    <strong>معالجة الطلبات:</strong>
                    راجع واعتمد طلبات التفعيل الجديدة
                </li>
            </ol>
        </div>
        
        <!-- الأسئلة الشائعة -->
        <div class="guide-section">
            <h2><i class="fas fa-question-circle"></i> الأسئلة الشائعة</h2>
            
            <div class="accordion" onclick="toggleAccordion(this)">
                <div class="accordion-header">
                    <strong>كيف أنشئ ترخيص جديد؟</strong>
                    <i class="fas fa-chevron-down" style="float: left;"></i>
                </div>
                <div class="accordion-content">
                    <p>1. اذهب إلى قسم "إدارة التراخيص"</p>
                    <p>2. انقر على "إنشاء ترخيص جديد"</p>
                    <p>3. املأ بيانات العميل والترخيص</p>
                    <p>4. اختر نوع ومدة الترخيص</p>
                    <p>5. انقر "إنشاء الترخيص"</p>
                </div>
            </div>
            
            <div class="accordion" onclick="toggleAccordion(this)">
                <div class="accordion-header">
                    <strong>كيف أراجع طلبات التفعيل؟</strong>
                    <i class="fas fa-chevron-down" style="float: left;"></i>
                </div>
                <div class="accordion-content">
                    <p>1. انتقل إلى قسم "طلبات التفعيل"</p>
                    <p>2. راجع قائمة الطلبات المعلقة</p>
                    <p>3. انقر على "عرض التفاصيل" لكل طلب</p>
                    <p>4. اختر "موافقة" أو "رفض" حسب الحاجة</p>
                </div>
            </div>
            
            <div class="accordion" onclick="toggleAccordion(this)">
                <div class="accordion-header">
                    <strong>كيف أصدر التراخيص؟</strong>
                    <i class="fas fa-chevron-down" style="float: left;"></i>
                </div>
                <div class="accordion-content">
                    <p>1. في قسم "إدارة التراخيص"</p>
                    <p>2. حدد التراخيص المطلوبة</p>
                    <p>3. انقر على "تصدير التراخيص"</p>
                    <p>4. اختر تنسيق التصدير (CSV, Excel, PDF)</p>
                </div>
            </div>
            
            <div class="accordion" onclick="toggleAccordion(this)">
                <div class="accordion-header">
                    <strong>ماذا لو لم تعمل الأقسام؟</strong>
                    <i class="fas fa-chevron-down" style="float: left;"></i>
                </div>
                <div class="accordion-content">
                    <p>1. استخدم لوحة التحكم المُصلحة: admin-panel-fixed.html</p>
                    <p>2. أو استخدم أداة التشخيص: debug-admin-panel.html</p>
                    <p>3. أو اتبع تعليمات الإصلاح اليدوي في صفحة الاختبار</p>
                </div>
            </div>
        </div>
        
        <!-- نصائح وحيل -->
        <div class="guide-section">
            <h2><i class="fas fa-lightbulb"></i> نصائح وحيل</h2>
            
            <div class="tip-box">
                <h4><i class="fas fa-info-circle"></i> نصيحة:</h4>
                <p>استخدم اختصارات لوحة المفاتيح للتنقل السريع بين الأقسام. يمكنك أيضاً استخدام البحث السريع في كل قسم.</p>
            </div>
            
            <div class="success-box">
                <h4><i class="fas fa-check-circle"></i> أفضل الممارسات:</h4>
                <p>راجع لوحة المعلومات يومياً لمتابعة الأنشطة الجديدة، وتأكد من معالجة طلبات التفعيل في الوقت المناسب.</p>
            </div>
            
            <div class="warning-box">
                <h4><i class="fas fa-exclamation-triangle"></i> تحذير:</h4>
                <p>تأكد من حفظ التغييرات قبل الانتقال بين الأقسام، واحرص على عمل نسخ احتياطية دورية من البيانات.</p>
            </div>
        </div>
        
        <!-- فيديوهات تعليمية -->
        <div class="guide-section">
            <h2><i class="fas fa-video"></i> فيديوهات تعليمية</h2>
            
            <div class="video-placeholder">
                <i class="fas fa-play-circle" style="font-size: 3rem; margin-bottom: 15px;"></i>
                <h3>فيديو تعليمي: جولة في لوحة التحكم</h3>
                <p>شرح مفصل لجميع أقسام وميزات لوحة التحكم</p>
                <button style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin-top: 15px;">
                    مشاهدة الفيديو
                </button>
            </div>
        </div>
        
        <!-- الدعم الفني -->
        <div class="guide-section">
            <h2><i class="fas fa-headset"></i> الدعم الفني</h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                    <i class="fas fa-phone" style="font-size: 2rem; color: #28a745; margin-bottom: 10px;"></i>
                    <h4>الهاتف</h4>
                    <p style="font-size: 1.2rem; font-weight: bold;">0696924176</p>
                </div>
                
                <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                    <i class="fab fa-whatsapp" style="font-size: 2rem; color: #25d366; margin-bottom: 10px;"></i>
                    <h4>واتساب</h4>
                    <p style="font-size: 1.2rem; font-weight: bold;">0696924176</p>
                </div>
                
                <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                    <i class="fas fa-clock" style="font-size: 2rem; color: #007bff; margin-bottom: 10px;"></i>
                    <h4>أوقات العمل</h4>
                    <p style="font-size: 1.2rem; font-weight: bold;">8:00 ص - 8:00 م</p>
                </div>
            </div>
        </div>
        
        <!-- روابط سريعة -->
        <div class="guide-section" style="text-align: center;">
            <h2><i class="fas fa-external-link-alt"></i> روابط سريعة</h2>
            
            <div style="margin-top: 30px;">
                <a href="admin-panel-fixed.html" class="quick-link success">
                    <i class="fas fa-tachometer-alt"></i> لوحة التحكم المُصلحة
                </a>
                <a href="admin-panel-simple.html" class="quick-link">
                    <i class="fas fa-th-large"></i> لوحة التحكم المبسطة
                </a>
                <a href="test-admin-panel.html" class="quick-link warning">
                    <i class="fas fa-vial"></i> صفحة الاختبار
                </a>
                <a href="debug-admin-panel.html" class="quick-link danger">
                    <i class="fas fa-bug"></i> أداة التشخيص
                </a>
                <a href="../../index.html" class="quick-link">
                    <i class="fas fa-home"></i> التطبيق الرئيسي
                </a>
            </div>
        </div>
    </div>

    <script>
        function toggleAccordion(element) {
            element.classList.toggle('active');
            
            // إغلاق الأكورديونات الأخرى
            document.querySelectorAll('.accordion').forEach(acc => {
                if (acc !== element) {
                    acc.classList.remove('active');
                }
            });
        }
        
        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', () => {
            // تأثير التمرير السلس
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth'
                        });
                    }
                });
            });
            
            // تأثير الظهور التدريجي
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);
            
            document.querySelectorAll('.guide-section').forEach(section => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(section);
            });
        });
    </script>
</body>
</html>
