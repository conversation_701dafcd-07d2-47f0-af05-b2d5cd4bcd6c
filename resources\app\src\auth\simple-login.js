// نظام تسجيل دخول مبسط
class SimpleLoginSystem {
    constructor() {
        this.init();
    }

    init() {
        console.log('🔐 تهيئة نظام تسجيل الدخول المبسط...');
        this.setupEventListeners();
        this.updateSystemTime();
        this.checkExistingSession();
        
        // تحديث الوقت كل ثانية
        setInterval(() => this.updateSystemTime(), 1000);
    }

    setupEventListeners() {
        // نموذج تسجيل الدخول
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // تبديل إظهار كلمة المرور
        const togglePassword = document.getElementById('toggle-password');
        if (togglePassword) {
            togglePassword.addEventListener('click', () => this.togglePasswordVisibility());
        }

        // أزرار أخرى
        const activationBtn = document.getElementById('activation-btn');
        if (activationBtn) {
            activationBtn.addEventListener('click', () => this.openActivationModal());
        }

        const contactBtn = document.getElementById('contact-btn');
        if (contactBtn) {
            contactBtn.addEventListener('click', () => this.openContactModal());
        }
    }

    async handleLogin(event) {
        event.preventDefault();
        
        const username = document.getElementById('username')?.value.trim();
        const password = document.getElementById('password')?.value;
        
        if (!username || !password) {
            this.showMessage('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
            return;
        }

        // إظهار حالة التحميل
        this.setLoading(true);
        
        try {
            // محاولة تسجيل الدخول
            const result = await this.authenticateUser(username, password);
            
            if (result.success) {
                this.showMessage('تم تسجيل الدخول بنجاح!', 'success');
                
                // حفظ بيانات الجلسة
                this.saveSession(result.user);
                
                // إعادة توجيه بعد تأخير قصير
                setTimeout(() => {
                    this.redirectToMainApp();
                }, 1500);
            } else {
                this.showMessage(result.message || 'فشل في تسجيل الدخول', 'error');
            }
            
        } catch (error) {
            console.error('خطأ في تسجيل الدخول:', error);
            this.showMessage('حدث خطأ في النظام', 'error');
        } finally {
            this.setLoading(false);
        }
    }

    async authenticateUser(username, password) {
        // بيانات المستخدمين الصالحة
        const validUsers = {
            'admin': { password: 'admin123', role: 'admin', name: 'المدير العام' },
            'manager': { password: 'manager123', role: 'manager', name: 'المدير' },
            'user': { password: 'user123', role: 'user', name: 'المستخدم' },
            'employee': { password: 'employee123', role: 'employee', name: 'الموظف' }
        };

        // محاكاة تأخير الشبكة
        await new Promise(resolve => setTimeout(resolve, 1000));

        const user = validUsers[username];
        if (user && user.password === password) {
            return {
                success: true,
                user: {
                    username: username,
                    name: user.name,
                    role: user.role,
                    loginTime: new Date().toISOString()
                }
            };
        } else {
            return {
                success: false,
                message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
            };
        }
    }

    saveSession(user) {
        try {
            localStorage.setItem('isLoggedIn', 'true');
            localStorage.setItem('currentUser', JSON.stringify(user));
            localStorage.setItem('loginTime', new Date().toISOString());
            
            // حفظ في AuthManager إذا كان متاحاً
            if (window.AuthManager) {
                window.AuthManager.setCurrentUser(user);
            }
        } catch (error) {
            console.error('خطأ في حفظ الجلسة:', error);
        }
    }

    checkExistingSession() {
        try {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            if (isLoggedIn === 'true') {
                console.log('المستخدم مسجل الدخول مسبقاً');
                // يمكن إعادة التوجيه مباشرة أو عرض رسالة
                this.showMessage('أنت مسجل الدخول بالفعل', 'info');
            }
        } catch (error) {
            console.error('خطأ في فحص الجلسة:', error);
        }
    }

    redirectToMainApp() {
        // فحص إذا كان هناك صفحة محفوظة للعودة إليها
        const returnUrl = sessionStorage.getItem('returnUrl');

        if (returnUrl && returnUrl !== window.location.href) {
            // العودة للصفحة المطلوبة
            sessionStorage.removeItem('returnUrl');
            window.location.href = returnUrl;
        } else {
            // العودة للصفحة الرئيسية
            window.location.href = 'index.html';
        }
    }

    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.querySelector('#toggle-password i');
        
        if (passwordInput && toggleIcon) {
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }
    }

    setLoading(loading) {
        const submitBtn = document.querySelector('button[type="submit"]');
        const form = document.getElementById('login-form');
        
        if (submitBtn) {
            submitBtn.disabled = loading;
            submitBtn.innerHTML = loading ? 
                '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...' : 
                '<i class="fas fa-sign-in-alt"></i> تسجيل الدخول';
        }
        
        if (form) {
            form.style.opacity = loading ? '0.7' : '1';
        }
    }

    showMessage(message, type = 'info') {
        // إنشاء عنصر الرسالة
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.innerHTML = `
            <i class="fas ${this.getMessageIcon(type)}"></i>
            <span>${message}</span>
        `;
        
        // إضافة الرسالة للصفحة
        const container = document.querySelector('.login-card') || document.body;
        container.appendChild(messageDiv);
        
        // إزالة الرسالة بعد 5 ثوان
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 5000);
    }

    getMessageIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    updateSystemTime() {
        const timeElement = document.getElementById('system-time');
        if (timeElement) {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            timeElement.textContent = timeString;
        }
    }

    openActivationModal() {
        // فتح نافذة تفعيل الترخيص
        window.open('../../activate-license.html', '_blank');
    }

    openContactModal() {
        // عرض معلومات الاتصال
        alert(`
معلومات الاتصال:
المطور: ISHQK
الهاتف/واتساب: **********
البريد الإلكتروني: <EMAIL>
        `);
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 تحميل نظام تسجيل الدخول...');
    window.SimpleLoginSystem = new SimpleLoginSystem();
});

// إضافة أنماط CSS للرسائل
const messageStyles = `
<style>
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 300px;
    animation: slideIn 0.3s ease-out;
}

.message.success {
    background: #2ecc71;
}

.message.error {
    background: #e74c3c;
}

.message.warning {
    background: #f39c12;
}

.message.info {
    background: #3498db;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>
`;

document.head.insertAdjacentHTML('beforeend', messageStyles);
