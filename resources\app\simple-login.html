<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - مؤسسة وقود المستقبل</title>
    <link rel="icon" type="image/x-icon" href="assets/icons/app-icon.ico">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            padding: 20px;
        }
        
        .login-container {
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 450px;
            text-align: center;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            color: white;
            font-size: 2rem;
        }
        
        h1 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 2rem;
            font-size: 1rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
            text-align: right;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        .input-container {
            position: relative;
        }
        
        .input-container input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .input-container input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .input-container i {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }
        
        .password-toggle {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            padding: 0.5rem;
        }
        
        .password-toggle:hover {
            color: #667eea;
        }
        
        .login-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .login-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }
        
        .demo-credentials {
            margin-top: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #e1e5e9;
        }
        
        .demo-credentials h4 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
        
        .credential-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            margin: 0.5rem 0;
            background: white;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.85rem;
        }
        
        .credential-item:hover {
            background: #e3f2fd;
            transform: translateX(-5px);
        }
        
        .credential-item strong {
            color: #333;
        }
        
        .credential-item span {
            color: #666;
            font-family: monospace;
        }
        
        .links {
            margin-top: 2rem;
            display: flex;
            justify-content: space-between;
            gap: 1rem;
        }
        
        .link {
            color: #667eea;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .link:hover {
            color: #764ba2;
            text-decoration: underline;
        }
        
        .system-time {
            margin-top: 1rem;
            padding: 0.5rem;
            background: #e8f5e8;
            border-radius: 5px;
            font-size: 0.8rem;
            color: #2e7d32;
        }
        
        @media (max-width: 768px) {
            .login-container {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            .links {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <i class="fas fa-gas-pump"></i>
        </div>
        
        <h1>مؤسسة وقود المستقبل</h1>
        <p class="subtitle">نظام الإدارة المتكامل - الإصدار 2.2.0</p>
        
        <form id="login-form">
            <div class="form-group">
                <label for="username">اسم المستخدم</label>
                <div class="input-container">
                    <i class="fas fa-user"></i>
                    <input type="text" id="username" name="username" required 
                           placeholder="أدخل اسم المستخدم" autocomplete="username">
                </div>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور</label>
                <div class="input-container">
                    <i class="fas fa-lock"></i>
                    <input type="password" id="password" name="password" required 
                           placeholder="أدخل كلمة المرور" autocomplete="current-password">
                    <button type="button" class="password-toggle" id="toggle-password">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
            
            <button type="submit" class="login-btn">
                <i class="fas fa-sign-in-alt"></i>
                تسجيل الدخول
            </button>
        </form>
        
        <div class="demo-credentials">
            <h4>بيانات تسجيل الدخول التجريبية:</h4>
            <div class="credential-item" onclick="fillCredentials('admin', 'admin123')">
                <strong>المدير العام:</strong>
                <span>admin / admin123</span>
            </div>
            <div class="credential-item" onclick="fillCredentials('manager', 'manager123')">
                <strong>المدير:</strong>
                <span>manager / manager123</span>
            </div>
            <div class="credential-item" onclick="fillCredentials('user', 'user123')">
                <strong>المستخدم:</strong>
                <span>user / user123</span>
            </div>
            <div class="credential-item" onclick="fillCredentials('employee', 'employee123')">
                <strong>الموظف:</strong>
                <span>employee / employee123</span>
            </div>
        </div>
        
        <div class="links">
            <a href="activate-license.html" class="link">
                <i class="fas fa-key"></i>
                تفعيل ترخيص جديد
            </a>
            <a href="#" class="link" onclick="showContact()">
                <i class="fas fa-phone"></i>
                الدعم الفني
            </a>
        </div>
        
        <div class="system-time">
            <i class="fas fa-clock"></i>
            <span id="system-time"></span>
        </div>
    </div>

    <!-- الأنظمة الأساسية -->
    <script src="src/core/auth-manager.js"></script>
    <script src="src/core/data-manager.js"></script>
    
    <!-- نظام تسجيل الدخول المبسط -->
    <script src="src/auth/simple-login.js"></script>
    
    <script>
        // وظائف مساعدة
        function fillCredentials(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
        }
        
        function showContact() {
            alert(`
معلومات الدعم الفني:

المطور: ISHQK
الهاتف/واتساب: 0696924176
البريد الإلكتروني: <EMAIL>

متاح من 8:00 صباحاً إلى 8:00 مساءً
            `);
        }
        
        // تحديث الوقت
        function updateTime() {
            const timeElement = document.getElementById('system-time');
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            timeElement.textContent = timeString;
        }
        
        // تحديث الوقت كل ثانية
        setInterval(updateTime, 1000);
        updateTime();
    </script>
</body>
</html>
