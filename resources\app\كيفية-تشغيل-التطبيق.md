# 🚀 كيفية تشغيل التطبيق

## طرق التشغيل المختلفة:

### 1. التشغيل السريع (موصى به)
```
double-click: quick-start.bat
```

### 2. التشغيل الكامل مع فحص التبعيات
```
double-click: launch-app.bat
```

### 3. التشغيل من سطر الأوامر
```bash
npm start
```

### 4. التشغيل المباشر لـ Electron
```bash
node_modules\.bin\electron.cmd main.js
```

## 📋 متطلبات التشغيل:

- ✅ Node.js (مثبت)
- ✅ npm (مثبت مع Node.js)
- ✅ Electron (مثبت تلقائياً)

## 🎯 نقاط الدخول للتطبيق:

1. **start.html** - صفحة التشغيل السريع
2. **app-launcher.html** - مشغل التطبيق الذكي
3. **dashboard.html** - لوحة التحكم الرئيسية
4. **login.html** - صفحة تسجيل الدخول

## 🔐 بيانات تسجيل الدخول:

| المستخدم | كلمة المرور | الصلاحية |
|----------|-------------|----------|
| admin | admin123 | مدير عام |
| manager | manager123 | مدير |
| user | user123 | مستخدم |
| employee | employee123 | موظف |

## 🔑 مفاتيح التراخيص:

- `FF-DEMO-2024-TEST` (تجريبي - 30 يوم)
- `FF-BASIC-2024-001` (أساسي - سنة)
- `FF-PREMIUM-2024-PRO` (مميز - سنتان)

## 🛠️ استكشاف الأخطاء:

### إذا لم يعمل التطبيق:
1. تأكد من تثبيت Node.js
2. شغل `npm install` في مجلد التطبيق
3. جرب `npm start`
4. إذا فشل، جرب التشغيل المباشر

### إذا ظهرت أخطاء:
1. احذف مجلد `node_modules`
2. شغل `npm install`
3. شغل `npm start`

## 📞 الدعم الفني:

- **المطور:** ISHQK
- **الهاتف/واتساب:** 0696924176
- **البريد الإلكتروني:** <EMAIL>

## 📝 ملاحظات:

- التطبيق يعمل بنظام Electron
- يدعم جميع أنظمة التشغيل
- واجهة باللغة العربية
- قاعدة بيانات محلية
- نظام أمان متقدم

---

**الإصدار:** 2.2.0  
**تاريخ التحديث:** 2024  
**الترخيص:** MIT
