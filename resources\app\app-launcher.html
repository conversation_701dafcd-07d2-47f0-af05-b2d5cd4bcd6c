<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مؤسسة وقود المستقبل - نظام الإدارة المتكامل</title>
    <link rel="icon" type="image/x-icon" href="assets/icons/app-icon.ico">
    <link rel="manifest" href="manifest.webmanifest">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* أنماط مشغل التطبيق */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            overflow: hidden;
        }
        
        /* خلفية متحركة */
        .animated-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }
        
        .floating-shapes {
            position: relative;
            width: 100%;
            height: 100%;
        }
        
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape-1 { width: 80px; height: 80px; top: 10%; left: 10%; animation-delay: 0s; }
        .shape-2 { width: 120px; height: 120px; top: 20%; right: 15%; animation-delay: 1s; }
        .shape-3 { width: 60px; height: 60px; bottom: 20%; left: 20%; animation-delay: 2s; }
        .shape-4 { width: 100px; height: 100px; bottom: 30%; right: 10%; animation-delay: 3s; }
        .shape-5 { width: 140px; height: 140px; top: 50%; left: 50%; animation-delay: 4s; }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 1;
            }
        }
        
        /* الحاوي الرئيسي */
        .launcher-container {
            text-align: center;
            color: white;
            padding: 3rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 600px;
            width: 90%;
            position: relative;
            z-index: 1;
        }
        
        .app-logo {
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3.5rem;
            color: #667eea;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            opacity: 0.9;
            font-weight: 400;
        }
        
        .description {
            font-size: 1.1rem;
            margin-bottom: 3rem;
            opacity: 0.8;
            line-height: 1.6;
        }
        
        /* حالة التحميل */
        .loading-section {
            margin: 2rem 0;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        /* خيارات التشغيل */
        .launch-options {
            display: none;
            flex-direction: column;
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        .launch-options.show {
            display: flex;
        }
        
        .option-btn {
            padding: 1rem 2rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 15px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
        }
        
        .option-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }
        
        .option-btn.primary {
            background: rgba(33, 150, 243, 0.8);
            border-color: rgba(33, 150, 243, 0.9);
        }
        
        .option-btn.primary:hover {
            background: rgba(33, 150, 243, 0.9);
        }
        
        .option-btn i {
            font-size: 1.3rem;
        }
        
        /* رسائل الحالة */
        .status-message {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            font-size: 0.9rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .status-message.error {
            background: rgba(244, 67, 54, 0.2);
            border-color: rgba(244, 67, 54, 0.4);
        }
        
        .status-message.success {
            background: rgba(76, 175, 80, 0.2);
            border-color: rgba(76, 175, 80, 0.4);
        }
        
        /* معلومات النظام */
        .system-info {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            font-size: 0.85rem;
            opacity: 0.7;
        }
        
        .system-info div {
            margin: 0.25rem 0;
        }
        
        /* الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .launcher-container {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .subtitle {
                font-size: 1.1rem;
            }
            
            .description {
                font-size: 1rem;
            }
            
            .app-logo {
                width: 100px;
                height: 100px;
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <!-- خلفية متحركة -->
    <div class="animated-background">
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
        </div>
    </div>

    <!-- الحاوي الرئيسي -->
    <div class="launcher-container">
        <div class="app-logo">
            <i class="fas fa-gas-pump"></i>
        </div>
        
        <h1>مؤسسة وقود المستقبل</h1>
        <div class="subtitle">نظام الإدارة المتكامل</div>
        <div class="description">
            نظام شامل لإدارة محطات الوقود والغاز الطبيعي مع إدارة العملاء والمركبات والشهادات
        </div>

        <!-- قسم التحميل -->
        <div class="loading-section" id="loading-section">
            <div class="loading-spinner"></div>
            <div class="loading-text" id="loading-text">جاري فحص النظام...</div>
        </div>

        <!-- خيارات التشغيل -->
        <div class="launch-options" id="launch-options">
            <a href="dashboard.html" class="option-btn primary" id="dashboard-btn">
                <i class="fas fa-tachometer-alt"></i>
                <span>دخول لوحة التحكم</span>
            </a>
            
            <a href="login.html" class="option-btn" id="login-btn">
                <i class="fas fa-sign-in-alt"></i>
                <span>تسجيل الدخول</span>
            </a>
            
            <a href="activate-license.html" class="option-btn" id="activate-btn">
                <i class="fas fa-key"></i>
                <span>تفعيل ترخيص جديد</span>
            </a>
        </div>

        <!-- رسائل الحالة -->
        <div class="status-message" id="status-message" style="display: none;"></div>

        <!-- معلومات النظام -->
        <div class="system-info">
            <div><strong>الإصدار:</strong> 2.2.0</div>
            <div><strong>المطور:</strong> ISHQK</div>
            <div><strong>الدعم:</strong> **********</div>
        </div>
    </div>

    <!-- الأنظمة الأساسية -->
    <script src="src/core/auth-manager.js"></script>
    <script src="src/core/data-manager.js"></script>

    <script>
        // مشغل التطبيق
        class AppLauncher {
            constructor() {
                this.init();
            }

            async init() {
                console.log('🚀 تشغيل مشغل التطبيق...');
                
                try {
                    // فحص النظام
                    await this.checkSystem();
                    
                    // فحص المصادقة
                    const isAuthenticated = await this.checkAuthentication();
                    
                    // تحديث الواجهة
                    this.updateInterface(isAuthenticated);
                    
                } catch (error) {
                    console.error('❌ خطأ في تشغيل التطبيق:', error);
                    this.showError('فشل في تشغيل التطبيق');
                }
            }

            async checkSystem() {
                this.updateLoadingText('فحص الأنظمة الأساسية...');
                await this.delay(1000);
                
                // فحص توفر الأنظمة
                let systemsReady = 0;
                const totalSystems = 3;
                
                // فحص AuthManager
                if (window.AuthManager) {
                    systemsReady++;
                    this.updateLoadingText('نظام المصادقة جاهز...');
                } else {
                    this.updateLoadingText('انتظار نظام المصادقة...');
                    await this.waitForSystem(() => window.AuthManager);
                    systemsReady++;
                }
                
                await this.delay(500);
                
                // فحص DataManager
                if (window.DataManager) {
                    systemsReady++;
                    this.updateLoadingText('نظام البيانات جاهز...');
                } else {
                    this.updateLoadingText('انتظار نظام البيانات...');
                    await this.waitForSystem(() => window.DataManager);
                    systemsReady++;
                }
                
                await this.delay(500);
                
                // فحص localStorage
                try {
                    localStorage.setItem('test', 'test');
                    localStorage.removeItem('test');
                    systemsReady++;
                    this.updateLoadingText('نظام التخزين جاهز...');
                } catch (error) {
                    throw new Error('نظام التخزين غير متاح');
                }
                
                await this.delay(500);
                
                if (systemsReady === totalSystems) {
                    this.updateLoadingText('جميع الأنظمة جاهزة!');
                    await this.delay(1000);
                } else {
                    throw new Error('فشل في تحميل بعض الأنظمة');
                }
            }

            async checkAuthentication() {
                this.updateLoadingText('فحص حالة المصادقة...');
                await this.delay(500);
                
                if (window.AuthManager && window.AuthManager.isLoggedIn()) {
                    this.updateLoadingText('المستخدم مسجل الدخول');
                    return true;
                } else {
                    this.updateLoadingText('المستخدم غير مسجل الدخول');
                    return false;
                }
            }

            updateInterface(isAuthenticated) {
                const loadingSection = document.getElementById('loading-section');
                const launchOptions = document.getElementById('launch-options');
                const dashboardBtn = document.getElementById('dashboard-btn');
                
                // إخفاء قسم التحميل
                loadingSection.style.display = 'none';
                
                // إظهار خيارات التشغيل
                launchOptions.classList.add('show');
                
                if (isAuthenticated) {
                    // إبراز زر لوحة التحكم
                    dashboardBtn.style.order = '-1';
                    this.showSuccess('مرحباً بك! يمكنك الدخول مباشرة للوحة التحكم');
                    
                    // إعادة توجيه تلقائية بعد 3 ثوان
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 3000);
                } else {
                    this.showInfo('يرجى تسجيل الدخول للوصول إلى النظام');
                }
            }

            async waitForSystem(checkFunction) {
                const maxWait = 5000; // 5 ثوان
                const startTime = Date.now();
                
                while (Date.now() - startTime < maxWait) {
                    if (checkFunction()) {
                        return;
                    }
                    await this.delay(100);
                }
                
                throw new Error('انتهت مهلة انتظار النظام');
            }

            updateLoadingText(text) {
                const loadingText = document.getElementById('loading-text');
                if (loadingText) {
                    loadingText.textContent = text;
                }
            }

            showMessage(message, type) {
                const statusMessage = document.getElementById('status-message');
                statusMessage.textContent = message;
                statusMessage.className = `status-message ${type}`;
                statusMessage.style.display = 'block';
            }

            showSuccess(message) {
                this.showMessage(message, 'success');
            }

            showError(message) {
                this.showMessage(message, 'error');
            }

            showInfo(message) {
                this.showMessage(message, '');
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // تشغيل المشغل عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            new AppLauncher();
        });
    </script>
</body>
</html>
