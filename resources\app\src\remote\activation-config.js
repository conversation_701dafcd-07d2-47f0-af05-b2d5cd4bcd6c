// إعدادات نظام طلبات التفعيل المحسن
const ActivationConfig = {
    // إعدادات عامة
    general: {
        autoRefreshInterval: 30000, // 30 ثانية
        dataSaveInterval: 30000, // 30 ثانية
        maxRequestsPerPage: 20,
        enableNotifications: true,
        enableSounds: true,
        enableVisualEffects: true
    },

    // إعدادات الإشعارات
    notifications: {
        sound: {
            enabled: true,
            frequency: 800, // Hz
            duration: 300, // ms
            volume: 0.3
        },
        visual: {
            enabled: true,
            animationDuration: 2000, // ms
            pulseColor: 'rgba(52, 152, 219, 0.7)'
        },
        badge: {
            enabled: true,
            maxCount: 99,
            color: '#dc3545'
        }
    },

    // إعدادات التصفية
    filters: {
        search: {
            enabled: true,
            placeholder: 'ابحث بالاسم أو الهاتف...',
            minLength: 2
        },
        status: {
            enabled: true,
            options: [
                { value: '', label: 'جميع الحالات' },
                { value: 'pending', label: 'في الانتظار' },
                { value: 'approved', label: 'تم الموافقة' },
                { value: 'rejected', label: 'مرفوض' },
                { value: 'processing', label: 'قيد المعالجة' }
            ]
        },
        state: {
            enabled: true,
            options: [
                { value: '', label: 'جميع الولايات' },
                { value: 'الجزائر', label: 'الجزائر' },
                { value: 'وهران', label: 'وهران' },
                { value: 'قسنطينة', label: 'قسنطينة' },
                { value: 'تيزي وزو', label: 'تيزي وزو' },
                { value: 'بجاية', label: 'بجاية' },
                { value: 'سطيف', label: 'سطيف' },
                { value: 'عنابة', label: 'عنابة' },
                { value: 'باتنة', label: 'باتنة' },
                { value: 'ورقلة', label: 'ورقلة' },
                { value: 'بسكرة', label: 'بسكرة' }
            ]
        }
    },

    // إعدادات حالات الطلبات
    requestStatus: {
        pending: {
            label: 'في الانتظار',
            color: '#856404',
            backgroundColor: '#fff3cd',
            borderColor: '#ffeaa7',
            icon: 'fas fa-clock'
        },
        approved: {
            label: 'تم الموافقة',
            color: '#155724',
            backgroundColor: '#d4edda',
            borderColor: '#c3e6cb',
            icon: 'fas fa-check-circle'
        },
        rejected: {
            label: 'مرفوض',
            color: '#721c24',
            backgroundColor: '#f8d7da',
            borderColor: '#f5c6cb',
            icon: 'fas fa-times-circle'
        },
        processing: {
            label: 'قيد المعالجة',
            color: '#0c5460',
            backgroundColor: '#d1ecf1',
            borderColor: '#bee5eb',
            icon: 'fas fa-spinner'
        }
    },

    // إعدادات التراخيص
    licenses: {
        types: {
            basic: {
                label: 'أساسي',
                duration: 365, // أيام
                features: ['الميزات الأساسية']
            },
            standard: {
                label: 'قياسي',
                duration: 365,
                features: ['جميع الميزات الأساسية', 'الدعم الفني']
            },
            premium: {
                label: 'مميز',
                duration: 730, // سنتان
                features: ['جميع الميزات', 'الدعم المتقدم', 'التحديثات المجانية']
            },
            enterprise: {
                label: 'مؤسسي',
                duration: 1095, // ثلاث سنوات
                features: ['حلول مخصصة', 'دعم 24/7', 'تدريب مجاني']
            }
        },
        keyFormat: {
            prefix: 'FF',
            separator: '-',
            yearIncluded: true,
            randomLength: 8
        }
    },

    // إعدادات التخزين
    storage: {
        keys: {
            activationRequests: 'activationRequests',
            adminLicenses: 'adminLicenses',
            adminClients: 'adminClients',
            adminSettings: 'adminSettings'
        },
        encryption: {
            enabled: false, // يمكن تفعيلها لاحقاً
            algorithm: 'AES-256-GCM'
        },
        backup: {
            enabled: true,
            maxBackups: 10,
            autoBackup: true,
            backupInterval: 86400000 // 24 ساعة
        }
    },

    // إعدادات الواجهة
    ui: {
        theme: {
            primary: '#007bff',
            secondary: '#6c757d',
            success: '#28a745',
            danger: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        },
        animations: {
            enabled: true,
            duration: 300,
            easing: 'ease-out'
        },
        responsive: {
            breakpoints: {
                mobile: 768,
                tablet: 992,
                desktop: 1200
            }
        }
    },

    // إعدادات التحقق من صحة البيانات
    validation: {
        phone: {
            pattern: /^0[5-7][0-9]{8}$/,
            message: 'رقم الهاتف يجب أن يكون 10 أرقام ويبدأ بـ 05، 06، أو 07'
        },
        name: {
            minLength: 2,
            maxLength: 50,
            pattern: /^[\u0600-\u06FFa-zA-Z\s]+$/,
            message: 'الاسم يجب أن يحتوي على أحرف عربية أو إنجليزية فقط'
        },
        businessName: {
            minLength: 3,
            maxLength: 100,
            required: false
        }
    },

    // إعدادات الأمان
    security: {
        maxLoginAttempts: 3,
        sessionTimeout: 1800000, // 30 دقيقة
        csrfProtection: true,
        xssProtection: true,
        rateLimiting: {
            enabled: true,
            maxRequests: 100,
            timeWindow: 3600000 // ساعة واحدة
        }
    },

    // إعدادات التصدير
    export: {
        formats: ['json', 'csv', 'excel'],
        defaultFormat: 'json',
        includeMetadata: true,
        dateFormat: 'YYYY-MM-DD HH:mm:ss'
    },

    // إعدادات التقارير
    reports: {
        enabled: true,
        types: [
            'daily_requests',
            'weekly_summary',
            'monthly_stats',
            'status_breakdown'
        ],
        autoGenerate: false,
        emailReports: false
    },

    // إعدادات التطوير والاختبار
    development: {
        debugMode: false,
        mockData: true,
        apiSimulation: true,
        performanceMonitoring: true
    },

    // دوال مساعدة
    helpers: {
        // تنسيق التاريخ
        formatDate: (date) => {
            return new Date(date).toLocaleDateString('ar-DZ', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        },

        // تنسيق رقم الهاتف
        formatPhone: (phone) => {
            if (phone.length === 10) {
                return phone.replace(/(\d{4})(\d{3})(\d{3})/, '$1 $2 $3');
            }
            return phone;
        },

        // إنشاء معرف فريد
        generateId: () => {
            return Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        },

        // التحقق من صحة البيانات
        validateRequest: (request) => {
            const errors = [];
            
            if (!request.firstName || request.firstName.length < 2) {
                errors.push('الاسم الأول مطلوب ويجب أن يكون أكثر من حرفين');
            }
            
            if (!request.lastName || request.lastName.length < 2) {
                errors.push('اللقب مطلوب ويجب أن يكون أكثر من حرفين');
            }
            
            if (!ActivationConfig.validation.phone.pattern.test(request.phone)) {
                errors.push(ActivationConfig.validation.phone.message);
            }
            
            return {
                isValid: errors.length === 0,
                errors: errors
            };
        },

        // حساب الوقت المنقضي
        getTimeAgo: (dateString) => {
            if (!dateString) return 'غير محدد';
            
            const date = new Date(dateString);
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) {
                return 'منذ لحظات';
            } else if (diffInSeconds < 3600) {
                const minutes = Math.floor(diffInSeconds / 60);
                return `منذ ${minutes} دقيقة`;
            } else if (diffInSeconds < 86400) {
                const hours = Math.floor(diffInSeconds / 3600);
                return `منذ ${hours} ساعة`;
            } else {
                const days = Math.floor(diffInSeconds / 86400);
                return `منذ ${days} يوم`;
            }
        }
    }
};

// تصدير الإعدادات للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ActivationConfig;
} else if (typeof window !== 'undefined') {
    window.ActivationConfig = ActivationConfig;
}
