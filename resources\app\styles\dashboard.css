/* أنماط لوحة التحكم الرئيسية */

/* المتغيرات العامة */
:root {
    --primary-color: #2196F3;
    --secondary-color: #FFC107;
    --success-color: #4CAF50;
    --danger-color: #F44336;
    --warning-color: #FF9800;
    --info-color: #00BCD4;
    --dark-color: #212529;
    --light-color: #F8F9FA;
    --border-color: #DEE2E6;
    --text-color: #495057;
    --text-muted: #6C757D;
    --bg-color: #FFFFFF;
    --sidebar-bg: #1E293B;
    --sidebar-text: #E2E8F0;
    --header-bg: #FFFFFF;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-lg: 0 4px 6px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
    --font-family: 'Cairo', '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* الوضع المظلم */
[data-theme="dark"] {
    --bg-color: #1A202C;
    --text-color: #E2E8F0;
    --text-muted: #A0AEC0;
    --border-color: #2D3748;
    --header-bg: #2D3748;
    --light-color: #2D3748;
    --shadow: 0 2px 4px rgba(0,0,0,0.3);
    --shadow-lg: 0 4px 6px rgba(0,0,0,0.3);
}

/* إعادة تعيين الأنماط */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--light-color);
    color: var(--text-color);
    line-height: 1.6;
    direction: rtl;
    overflow-x: hidden;
}

/* شاشة التحميل */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-logo {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

.loading-text h2 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.loading-text p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* الحاوي الرئيسي */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* الشريط العلوي */
.main-header {
    background: var(--header-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 0 1rem;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--text-color);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background: var(--light-color);
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.app-logo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.app-title h1 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.app-subtitle {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.header-center {
    flex: 1;
    max-width: 500px;
    margin: 0 2rem;
}

.search-box {
    position: relative;
    width: 100%;
}

.search-box i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-color);
    color: var(--text-color);
    font-size: 0.9rem;
    transition: var(--transition);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.header-right {
    display: flex;
    align-items: center;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* أزرار الشريط العلوي */
.notification-btn,
.settings-btn,
.user-btn {
    background: none;
    border: none;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.notification-btn:hover,
.settings-btn:hover {
    background: var(--light-color);
}

.notification-badge {
    position: absolute;
    top: 0;
    left: 0;
    background: var(--danger-color);
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.user-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    color: var(--text-color);
}

.user-avatar {
    width: 32px;
    height: 32px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-name {
    font-weight: 500;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* التخطيط الرئيسي */
.main-layout {
    display: flex;
    flex: 1;
    min-height: calc(100vh - 70px);
}

/* الشريط الجانبي */
.sidebar {
    width: 280px;
    background: var(--sidebar-bg);
    color: var(--sidebar-text);
    display: flex;
    flex-direction: column;
    transition: var(--transition);
    position: relative;
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
}

.nav-menu {
    list-style: none;
}

.nav-section-title {
    padding: 0.5rem 1.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    color: var(--text-muted);
    margin-top: 1rem;
}

.nav-item {
    margin: 0.25rem 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
}

.nav-link.active {
    background: rgba(33, 150, 243, 0.2);
    border-left: 3px solid var(--primary-color);
}

.nav-link i {
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
}

.nav-badge {
    background: var(--danger-color);
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    margin-right: auto;
}

/* معلومات الترخيص */
.license-info {
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.2);
}

.license-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.license-status i {
    color: var(--success-color);
}

.license-expiry {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* منطقة المحتوى */
.content-area {
    flex: 1;
    padding: 2rem;
    background: var(--light-color);
    overflow-y: auto;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.section-header h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--dark-color);
}

.section-actions {
    display: flex;
    gap: 0.5rem;
}

/* الإحصائيات السريعة */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--bg-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.customers { background: var(--primary-color); }
.stat-icon.vehicles { background: var(--success-color); }
.stat-icon.cards { background: var(--warning-color); }
.stat-icon.appointments { background: var(--info-color); }

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* محتوى لوحة التحكم */
.dashboard-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.dashboard-row {
    display: flex;
    gap: 2rem;
}

.dashboard-col-4 { flex: 0 0 33.333%; }
.dashboard-col-6 { flex: 0 0 50%; }
.dashboard-col-8 { flex: 0 0 66.667%; }

/* البطاقات */
.card {
    background: var(--bg-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.card-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--light-color);
}

.card-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
}

.card-body {
    padding: 1.5rem;
}

/* الإجراءات السريعة */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.quick-action-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    background: var(--light-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
    cursor: pointer;
    font-size: 0.9rem;
}

.quick-action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* الجداول */
.table-container {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.data-table th,
.data-table td {
    padding: 0.75rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background: var(--light-color);
    font-weight: 600;
    color: var(--dark-color);
}

.data-table tbody tr:hover {
    background: var(--light-color);
}

/* الأزرار */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
}

.btn.primary {
    background: var(--primary-color);
    color: white;
}

.btn.primary:hover {
    background: #1976D2;
}

.btn.secondary {
    background: var(--light-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn.secondary:hover {
    background: var(--border-color);
}

/* القوائم المنسدلة */
.notification-dropdown,
.quick-settings,
.user-dropdown {
    position: relative;
}

.notification-menu,
.settings-menu,
.user-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    min-width: 250px;
    z-index: 1000;
    display: none;
}

.notification-menu.show,
.settings-menu.show,
.user-menu.show {
    display: block;
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.menu-item:hover {
    background: var(--light-color);
}

.menu-divider {
    height: 1px;
    background: var(--border-color);
    margin: 0.5rem 0;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -280px;
        top: 70px;
        height: calc(100vh - 70px);
        z-index: 999;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .content-area {
        padding: 1rem;
    }
    
    .dashboard-row {
        flex-direction: column;
    }
    
    .dashboard-col-4,
    .dashboard-col-6,
    .dashboard-col-8 {
        flex: 1;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .header-center {
        display: none;
    }
}
