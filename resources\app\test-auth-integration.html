<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار ربط المصادقة - مؤسسة وقود المستقبل</title>
    <link rel="icon" type="image/x-icon" href="assets/icons/app-icon.ico">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            padding: 20px;
        }
        
        .container {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        
        h1 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 2rem;
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }
        
        .test-section h3 {
            color: #495057;
            margin-bottom: 1rem;
        }
        
        .test-buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .btn {
            padding: 1rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .btn-primary {
            background: #2196F3;
            color: white;
        }
        
        .btn-success {
            background: #4CAF50;
            color: white;
        }
        
        .btn-warning {
            background: #FF9800;
            color: white;
        }
        
        .btn-danger {
            background: #F44336;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .status {
            margin: 1rem 0;
            padding: 1rem;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .flow-diagram {
            background: #fff;
            padding: 1.5rem;
            border-radius: 10px;
            margin: 1rem 0;
            border: 2px solid #e9ecef;
        }
        
        .flow-step {
            display: flex;
            align-items: center;
            margin: 0.5rem 0;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .flow-step i {
            margin-left: 0.5rem;
            color: #2196F3;
        }
        
        .credentials {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            text-align: right;
            font-size: 0.9rem;
        }
        
        .credentials h4 {
            margin-bottom: 0.5rem;
            color: #1976d2;
        }
        
        .cred-item {
            margin: 0.25rem 0;
            font-family: monospace;
            background: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 اختبار ربط المصادقة</h1>
        <p>اختبار تكامل نظام المصادقة مع الصفحة الرئيسية</p>
        
        <div class="test-section">
            <h3>📋 سير العمل المتوقع:</h3>
            <div class="flow-diagram">
                <div class="flow-step">
                    <i class="fas fa-play"></i>
                    <span>1. المستخدم يفتح index.html</span>
                </div>
                <div class="flow-step">
                    <i class="fas fa-search"></i>
                    <span>2. فحص حالة المصادقة</span>
                </div>
                <div class="flow-step">
                    <i class="fas fa-question-circle"></i>
                    <span>3. إذا لم يكن مسجل الدخول → إعادة توجيه لـ login.html</span>
                </div>
                <div class="flow-step">
                    <i class="fas fa-check-circle"></i>
                    <span>4. إذا كان مسجل الدخول → إظهار المحتوى الرئيسي</span>
                </div>
                <div class="flow-step">
                    <i class="fas fa-home"></i>
                    <span>5. بعد تسجيل الدخول → العودة للصفحة المطلوبة</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 اختبارات النظام:</h3>
            
            <div class="test-buttons">
                <a href="index.html" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    اختبار الصفحة الرئيسية (index.html)
                </a>
                
                <a href="login.html" class="btn btn-success">
                    <i class="fas fa-sign-in-alt"></i>
                    اختبار صفحة تسجيل الدخول
                </a>
                
                <a href="dashboard.html" class="btn btn-warning">
                    <i class="fas fa-tachometer-alt"></i>
                    اختبار لوحة التحكم المباشرة
                </a>
                
                <button class="btn btn-danger" onclick="clearAuth()">
                    <i class="fas fa-trash"></i>
                    مسح بيانات المصادقة (للاختبار)
                </button>
                
                <button class="btn btn-secondary" onclick="checkAuthStatus()">
                    <i class="fas fa-info-circle"></i>
                    فحص حالة المصادقة الحالية
                </button>
            </div>
        </div>

        <div class="credentials">
            <h4>🔐 بيانات تسجيل الدخول للاختبار:</h4>
            <div class="cred-item"><strong>المدير:</strong> admin / admin123</div>
            <div class="cred-item"><strong>المدير:</strong> manager / manager123</div>
            <div class="cred-item"><strong>المستخدم:</strong> user / user123</div>
            <div class="cred-item"><strong>الموظف:</strong> employee / employee123</div>
        </div>

        <div id="status-display"></div>

        <div class="test-section">
            <h3>📝 ملاحظات الاختبار:</h3>
            <ul style="text-align: right; margin: 1rem 0;">
                <li>تأكد من تحميل جميع الملفات الأساسية</li>
                <li>فحص console للرسائل التشخيصية</li>
                <li>اختبار السيناريوهات المختلفة</li>
                <li>التحقق من إعادة التوجيه الصحيحة</li>
                <li>اختبار العودة للصفحة المطلوبة</li>
            </ul>
        </div>
    </div>

    <!-- الأنظمة الأساسية -->
    <script src="src/core/auth-manager.js"></script>
    <script src="src/core/data-manager.js"></script>

    <script>
        // وظائف الاختبار
        function clearAuth() {
            try {
                // مسح جميع بيانات المصادقة
                localStorage.removeItem('authToken');
                localStorage.removeItem('currentUser');
                localStorage.removeItem('currentLicense');
                localStorage.removeItem('loginTime');
                localStorage.removeItem('isLoggedIn');
                sessionStorage.removeItem('returnUrl');
                
                showStatus('تم مسح جميع بيانات المصادقة', 'success');
                
                setTimeout(() => {
                    location.reload();
                }, 1000);
                
            } catch (error) {
                showStatus('خطأ في مسح البيانات: ' + error.message, 'error');
            }
        }

        async function checkAuthStatus() {
            try {
                // انتظار تحميل AuthManager
                if (!window.AuthManager) {
                    showStatus('انتظار تحميل نظام المصادقة...', 'info');
                    
                    setTimeout(checkAuthStatus, 1000);
                    return;
                }

                const isLoggedIn = window.AuthManager.isLoggedIn();
                const user = window.AuthManager.getCurrentUser();
                const license = window.AuthManager.getCurrentLicense();
                
                let statusText = `حالة تسجيل الدخول: ${isLoggedIn ? 'مسجل' : 'غير مسجل'}\n`;
                
                if (user) {
                    statusText += `المستخدم: ${user.username} (${user.role})\n`;
                }
                
                if (license) {
                    statusText += `الترخيص: ${license.type} - ينتهي في ${license.expiryDate}\n`;
                }
                
                const returnUrl = sessionStorage.getItem('returnUrl');
                if (returnUrl) {
                    statusText += `صفحة العودة: ${returnUrl}`;
                }
                
                showStatus(statusText, isLoggedIn ? 'success' : 'info');
                
            } catch (error) {
                showStatus('خطأ في فحص الحالة: ' + error.message, 'error');
            }
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status-display');
            statusDiv.innerHTML = `<div class="status ${type}">${message.replace(/\n/g, '<br>')}</div>`;
        }

        // فحص تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(checkAuthStatus, 1000);
        });
    </script>
</body>
</html>
