// سكريپت تسجيل الدخول المحسن
class EnhancedLoginManager {
    constructor() {
        this.isLoading = false;
        this.maxAttempts = 3;
        this.lockoutDuration = 15 * 60 * 1000; // 15 دقيقة
        
        this.init();
    }

    // تهيئة النظام
    init() {
        this.setupEventListeners();
        this.checkExistingSession();
        this.loadDemoCredentials();
        this.setupModals();
    }

    // فحص الجلسة الموجودة
    checkExistingSession() {
        // انتظار تحميل AuthManager
        const checkAuth = () => {
            if (window.AuthManager) {
                if (window.AuthManager.isLoggedIn()) {
                    console.log('✅ المستخدم مسجل الدخول مسبقاً، إعادة توجيه...');
                    this.redirectToDashboard();
                    return;
                }
            } else {
                setTimeout(checkAuth, 100);
            }
        };
        checkAuth();
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // نموذج تسجيل الدخول
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // تبديل إظهار كلمة المرور
        const passwordToggle = document.getElementById('password-toggle');
        if (passwordToggle) {
            passwordToggle.addEventListener('click', () => this.togglePasswordVisibility());
        }

        // بيانات الاعتماد التجريبية
        document.querySelectorAll('.credential-item').forEach(item => {
            item.addEventListener('click', () => this.fillCredentials(item));
        });

        // روابط إضافية
        const systemInfoBtn = document.getElementById('system-info');
        if (systemInfoBtn) {
            systemInfoBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showSystemInfo();
            });
        }

        const forgotPasswordBtn = document.getElementById('forgot-password');
        if (forgotPasswordBtn) {
            forgotPasswordBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showForgotPassword();
            });
        }

        // إغلاق النوافذ المنبثقة
        document.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => this.closeModals());
        });

        // إغلاق النوافذ عند النقر خارجها
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModals();
                }
            });
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModals();
            }
            if (e.key === 'Enter' && e.ctrlKey) {
                const loginForm = document.getElementById('login-form');
                if (loginForm) {
                    loginForm.dispatchEvent(new Event('submit'));
                }
            }
        });
    }

    // معالجة تسجيل الدخول
    async handleLogin(event) {
        event.preventDefault();

        if (this.isLoading) return;

        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('remember-me').checked;

        // التحقق من صحة البيانات
        if (!this.validateInput(username, password)) {
            return;
        }

        // فحص محاولات تسجيل الدخول
        if (this.isAccountLocked(username)) {
            this.showAlert('الحساب مقفل مؤقتاً. يرجى المحاولة لاحقاً.', 'error');
            return;
        }

        try {
            this.setLoading(true);
            this.clearAlerts();

            // انتظار تحميل AuthManager
            await this.waitForAuthManager();

            // محاولة تسجيل الدخول
            const result = await window.AuthManager.login(username, password, rememberMe);

            if (result.success) {
                this.showAlert('تم تسجيل الدخول بنجاح! جاري التحويل...', 'success');
                
                // مسح محاولات تسجيل الدخول الفاشلة
                this.clearFailedAttempts(username);
                
                // إعادة توجيه بعد تأخير قصير
                setTimeout(() => {
                    this.redirectToDashboard();
                }, 1500);
            }

        } catch (error) {
            console.error('❌ خطأ في تسجيل الدخول:', error);
            
            // تسجيل محاولة فاشلة
            this.recordFailedAttempt(username);
            
            // عرض رسالة الخطأ
            this.showAlert(error.message || 'فشل في تسجيل الدخول. يرجى المحاولة مرة أخرى.', 'error');
            
            // مسح كلمة المرور
            document.getElementById('password').value = '';
            document.getElementById('password').focus();

        } finally {
            this.setLoading(false);
        }
    }

    // انتظار تحميل AuthManager
    async waitForAuthManager() {
        const maxWait = 5000; // 5 ثوان
        const startTime = Date.now();
        
        while (Date.now() - startTime < maxWait) {
            if (window.AuthManager) {
                return;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        throw new Error('فشل في تحميل نظام المصادقة');
    }

    // التحقق من صحة البيانات المدخلة
    validateInput(username, password) {
        if (!username) {
            this.showAlert('يرجى إدخال اسم المستخدم', 'error');
            document.getElementById('username').focus();
            return false;
        }

        if (!password) {
            this.showAlert('يرجى إدخال كلمة المرور', 'error');
            document.getElementById('password').focus();
            return false;
        }

        if (username.length < 3) {
            this.showAlert('اسم المستخدم يجب أن يكون 3 أحرف على الأقل', 'error');
            document.getElementById('username').focus();
            return false;
        }

        if (password.length < 6) {
            this.showAlert('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
            document.getElementById('password').focus();
            return false;
        }

        return true;
    }

    // تسجيل محاولة فاشلة
    recordFailedAttempt(username) {
        const attempts = JSON.parse(localStorage.getItem('loginAttempts') || '{}');
        const now = Date.now();
        
        if (!attempts[username]) {
            attempts[username] = { count: 0, lastAttempt: now };
        }
        
        attempts[username].count++;
        attempts[username].lastAttempt = now;
        
        localStorage.setItem('loginAttempts', JSON.stringify(attempts));

        // تحذير إذا اقترب من الحد الأقصى
        const remainingAttempts = this.maxAttempts - attempts[username].count;
        if (remainingAttempts > 0 && remainingAttempts <= 2) {
            this.showAlert(`تبقى ${remainingAttempts} محاولة قبل قفل الحساب`, 'warning');
        }
    }

    // مسح محاولات فاشلة
    clearFailedAttempts(username) {
        const attempts = JSON.parse(localStorage.getItem('loginAttempts') || '{}');
        delete attempts[username];
        localStorage.setItem('loginAttempts', JSON.stringify(attempts));
    }

    // فحص قفل الحساب
    isAccountLocked(username) {
        const attempts = JSON.parse(localStorage.getItem('loginAttempts') || '{}');
        const userAttempts = attempts[username];
        
        if (!userAttempts) return false;
        
        const timeSinceLastAttempt = Date.now() - userAttempts.lastAttempt;
        
        if (userAttempts.count >= this.maxAttempts) {
            if (timeSinceLastAttempt < this.lockoutDuration) {
                const remainingTime = Math.ceil((this.lockoutDuration - timeSinceLastAttempt) / 60000);
                this.showAlert(`الحساب مقفل لمدة ${remainingTime} دقيقة`, 'error');
                return true;
            } else {
                // انتهت مدة القفل، مسح المحاولات
                this.clearFailedAttempts(username);
                return false;
            }
        }
        
        return false;
    }

    // تبديل إظهار كلمة المرور
    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.querySelector('#password-toggle i');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'fas fa-eye-slash';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'fas fa-eye';
        }
    }

    // ملء بيانات الاعتماد
    fillCredentials(item) {
        const username = item.dataset.username;
        const password = item.dataset.password;
        
        if (username && password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            
            // تأثير بصري
            item.style.background = 'rgba(33, 150, 243, 0.1)';
            setTimeout(() => {
                item.style.background = '';
            }, 300);
        }
    }

    // تحميل بيانات الاعتماد التجريبية
    loadDemoCredentials() {
        console.log('📋 تم تحميل بيانات الاعتماد التجريبية');
    }

    // إعداد النوافذ المنبثقة
    setupModals() {
        const systemInfoModal = document.getElementById('system-info-modal');
        const forgotPasswordModal = document.getElementById('forgot-password-modal');
        
        if (!systemInfoModal || !forgotPasswordModal) {
            console.warn('⚠️ بعض النوافذ المنبثقة غير موجودة');
        }
    }

    // عرض معلومات النظام
    showSystemInfo() {
        const modal = document.getElementById('system-info-modal');
        if (modal) {
            modal.classList.add('show');
        }
    }

    // عرض نافذة نسيان كلمة المرور
    showForgotPassword() {
        const modal = document.getElementById('forgot-password-modal');
        if (modal) {
            modal.classList.add('show');
        }
    }

    // إغلاق النوافذ المنبثقة
    closeModals() {
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.remove('show');
        });
    }

    // تعيين حالة التحميل
    setLoading(loading) {
        this.isLoading = loading;
        const loginBtn = document.getElementById('login-btn');
        const btnText = loginBtn.querySelector('.btn-text');
        const btnLoading = loginBtn.querySelector('.btn-loading');
        
        if (loading) {
            loginBtn.disabled = true;
            btnText.style.display = 'none';
            btnLoading.style.display = 'flex';
        } else {
            loginBtn.disabled = false;
            btnText.style.display = 'block';
            btnLoading.style.display = 'none';
        }
    }

    // عرض تنبيه
    showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alert-container');
        
        // مسح التنبيهات السابقة
        alertContainer.innerHTML = '';
        
        // إنشاء تنبيه جديد
        const alert = document.createElement('div');
        alert.className = `alert ${type}`;
        
        const icon = this.getAlertIcon(type);
        alert.innerHTML = `
            <i class="${icon}"></i>
            <span>${message}</span>
        `;
        
        alertContainer.appendChild(alert);
        
        // إزالة التنبيه بعد 5 ثوان
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }

    // الحصول على أيقونة التنبيه
    getAlertIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    // مسح التنبيهات
    clearAlerts() {
        const alertContainer = document.getElementById('alert-container');
        alertContainer.innerHTML = '';
    }

    // إعادة توجيه للوحة التحكم أو الصفحة المطلوبة
    redirectToDashboard() {
        // فحص إذا كان هناك صفحة محفوظة للعودة إليها
        const returnUrl = sessionStorage.getItem('returnUrl');

        if (returnUrl && returnUrl !== window.location.href) {
            // العودة للصفحة المطلوبة
            sessionStorage.removeItem('returnUrl');
            window.location.href = returnUrl;
            return;
        }

        // التحقق من وجود ملف لوحة التحكم
        const dashboardUrl = 'dashboard.html';

        // محاولة الوصول للوحة التحكم
        fetch(dashboardUrl, { method: 'HEAD' })
            .then(response => {
                if (response.ok) {
                    window.location.href = dashboardUrl;
                } else {
                    // استخدام الملف البديل
                    window.location.href = 'index.html';
                }
            })
            .catch(() => {
                // في حالة الخطأ، استخدام الملف البديل
                window.location.href = 'index.html';
            });
    }
}

// تهيئة مدير تسجيل الدخول عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.EnhancedLoginManager = new EnhancedLoginManager();
});

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedLoginManager;
}
