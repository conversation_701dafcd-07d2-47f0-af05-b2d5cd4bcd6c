// بيانات تجريبية لنظام طلبات التفعيل

const DemoData = {
    // طلبات التفعيل التجريبية
    activationRequests: [
        {
            id: 1734192001001,
            firstName: 'أحمد',
            lastName: 'بن محمد',
            phone: '0555123456',
            state: 'الجزائر',
            municipality: 'الجزائر الوسطى',
            businessName: 'محطة الأمل للوقود',
            notes: 'طلب تفعيل عادي',
            status: 'pending',
            requestDate: '2024-12-14T10:30:00.000Z',
            deviceInfo: {
                platform: 'Windows',
                arch: 'x64',
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        },
        {
            id: 1734192001002,
            firstName: 'فاطمة',
            lastName: 'بن علي',
            phone: '0666789012',
            state: 'وهران',
            municipality: 'وهران',
            businessName: 'محطة النور',
            notes: 'طلب سريع للتفعيل',
            status: 'pending',
            requestDate: '2024-12-14T11:15:00.000Z',
            deviceInfo: {
                platform: 'Windows',
                arch: 'x64'
            }
        },
        {
            id: 1734192001003,
            firstName: 'يوسف',
            lastName: 'مرادي',
            phone: '0777345678',
            state: 'قسنطينة',
            municipality: 'قسنطينة',
            businessName: 'محطة المستقبل',
            notes: 'طلب عاجل - يحتاج معالجة فورية',
            status: 'processing',
            requestDate: '2024-12-14T09:45:00.000Z',
            deviceInfo: {
                platform: 'Windows',
                arch: 'x64'
            }
        },
        {
            id: 1734192001004,
            firstName: 'خديجة',
            lastName: 'العربي',
            phone: '**********',
            state: 'تيزي وزو',
            municipality: 'تيزي وزو',
            businessName: 'محطة الجبال',
            notes: 'طلب للمنطقة الجبلية',
            status: 'approved',
            requestDate: '2024-12-13T16:20:00.000Z',
            approvedDate: '2024-12-14T08:30:00.000Z',
            licenseKey: 'FF-2024-ABC12345',
            deviceInfo: {
                platform: 'Windows',
                arch: 'x64'
            }
        },
        {
            id: 1734192001005,
            firstName: 'محمد',
            lastName: 'الصغير',
            phone: '**********',
            state: 'بجاية',
            municipality: 'بجاية',
            businessName: 'محطة الساحل',
            notes: 'طلب للمنطقة الساحلية',
            status: 'rejected',
            requestDate: '2024-12-13T14:10:00.000Z',
            rejectedDate: '2024-12-14T07:45:00.000Z',
            deviceInfo: {
                platform: 'Windows',
                arch: 'x64'
            }
        },
        {
            id: 1734192001006,
            firstName: 'عائشة',
            lastName: 'بوعلام',
            phone: '**********',
            state: 'سطيف',
            municipality: 'سطيف',
            businessName: 'محطة الهضاب',
            notes: 'طلب جديد',
            status: 'pending',
            requestDate: '2024-12-14T12:00:00.000Z',
            deviceInfo: {
                platform: 'Windows',
                arch: 'x64'
            }
        },
        {
            id: 1734192001007,
            firstName: 'عبد الرحمن',
            lastName: 'قاسمي',
            phone: '0555789456',
            state: 'عنابة',
            municipality: 'عنابة',
            businessName: 'محطة الشرق',
            notes: 'طلب للمنطقة الشرقية',
            status: 'pending',
            requestDate: '2024-12-14T13:30:00.000Z',
            deviceInfo: {
                platform: 'Windows',
                arch: 'x64'
            }
        },
        {
            id: 1734192001008,
            firstName: 'زينب',
            lastName: 'حمدي',
            phone: '**********',
            state: 'باتنة',
            municipality: 'باتنة',
            businessName: 'محطة الأوراس',
            notes: 'طلب للمنطقة الجبلية',
            status: 'approved',
            requestDate: '2024-12-13T11:20:00.000Z',
            approvedDate: '2024-12-14T09:15:00.000Z',
            licenseKey: 'FF-2024-DEF67890',
            deviceInfo: {
                platform: 'Windows',
                arch: 'x64'
            }
        }
    ],

    // التراخيص التجريبية
    licenses: [
        {
            id: 'LIC-001',
            key: 'FF-2024-ABC12345',
            clientName: 'خديجة العربي',
            phone: '**********',
            state: 'تيزي وزو',
            municipality: 'تيزي وزو',
            businessName: 'محطة الجبال',
            type: 'standard',
            duration: 365,
            createdDate: '2024-12-14T08:30:00.000Z',
            expiryDate: '2025-12-14T08:30:00.000Z',
            status: 'active',
            requestId: 1734192001004
        },
        {
            id: 'LIC-002',
            key: 'FF-2024-DEF67890',
            clientName: 'زينب حمدي',
            phone: '**********',
            state: 'باتنة',
            municipality: 'باتنة',
            businessName: 'محطة الأوراس',
            type: 'premium',
            duration: 730,
            createdDate: '2024-12-14T09:15:00.000Z',
            expiryDate: '2026-12-14T09:15:00.000Z',
            status: 'active',
            requestId: 1734192001008
        }
    ],

    // العملاء التجريبيون
    clients: [
        {
            id: 1,
            name: 'خديجة العربي',
            phone: '**********',
            state: 'تيزي وزو',
            municipality: 'تيزي وزو',
            businessName: 'محطة الجبال',
            status: 'active',
            joinDate: '2024-12-14T08:30:00.000Z',
            licenseKey: 'FF-2024-ABC12345'
        },
        {
            id: 2,
            name: 'زينب حمدي',
            phone: '**********',
            state: 'باتنة',
            municipality: 'باتنة',
            businessName: 'محطة الأوراس',
            status: 'active',
            joinDate: '2024-12-14T09:15:00.000Z',
            licenseKey: 'FF-2024-DEF67890'
        }
    ],

    // دوال لإدارة البيانات التجريبية
    utils: {
        // تحميل البيانات التجريبية
        loadDemoData: function() {
            try {
                // حفظ طلبات التفعيل
                localStorage.setItem('activationRequests', JSON.stringify(this.activationRequests));
                
                // حفظ التراخيص
                localStorage.setItem('adminLicenses', JSON.stringify(this.licenses));
                
                // حفظ العملاء
                localStorage.setItem('adminClients', JSON.stringify(this.clients));
                
                console.log('✅ تم تحميل البيانات التجريبية بنجاح');
                return true;
            } catch (error) {
                console.error('❌ خطأ في تحميل البيانات التجريبية:', error);
                return false;
            }
        },

        // مسح جميع البيانات
        clearAllData: function() {
            try {
                localStorage.removeItem('activationRequests');
                localStorage.removeItem('adminLicenses');
                localStorage.removeItem('adminClients');
                localStorage.removeItem('adminSettings');
                
                console.log('✅ تم مسح جميع البيانات');
                return true;
            } catch (error) {
                console.error('❌ خطأ في مسح البيانات:', error);
                return false;
            }
        },

        // إنشاء طلب تجريبي جديد
        createRandomRequest: function() {
            const names = [
                { first: 'محمد', last: 'بن أحمد' },
                { first: 'فاطمة', last: 'العلوي' },
                { first: 'عبد الله', last: 'المغربي' },
                { first: 'خديجة', last: 'التونسي' },
                { first: 'يوسف', last: 'الجزائري' },
                { first: 'عائشة', last: 'المصري' }
            ];

            const states = [
                { state: 'الجزائر', municipality: 'الجزائر الوسطى' },
                { state: 'وهران', municipality: 'وهران' },
                { state: 'قسنطينة', municipality: 'قسنطينة' },
                { state: 'تيزي وزو', municipality: 'تيزي وزو' },
                { state: 'بجاية', municipality: 'بجاية' },
                { state: 'سطيف', municipality: 'سطيف' }
            ];

            const businesses = [
                'محطة الأمل',
                'محطة النور',
                'محطة المستقبل',
                'محطة الجبال',
                'محطة الساحل',
                'محطة الصحراء'
            ];

            const randomName = names[Math.floor(Math.random() * names.length)];
            const randomLocation = states[Math.floor(Math.random() * states.length)];
            const randomBusiness = businesses[Math.floor(Math.random() * businesses.length)];

            const request = {
                id: Date.now(),
                firstName: randomName.first,
                lastName: randomName.last,
                phone: '0' + Math.floor(Math.random() * 900000000 + 100000000),
                state: randomLocation.state,
                municipality: randomLocation.municipality,
                businessName: randomBusiness,
                notes: 'طلب تجريبي تم إنشاؤه تلقائياً',
                status: 'pending',
                requestDate: new Date().toISOString(),
                deviceInfo: {
                    platform: 'Windows',
                    arch: 'x64'
                }
            };

            // إضافة الطلب إلى localStorage
            const stored = JSON.parse(localStorage.getItem('activationRequests') || '[]');
            stored.push(request);
            localStorage.setItem('activationRequests', JSON.stringify(stored));

            console.log('✅ تم إنشاء طلب تجريبي جديد:', request.firstName + ' ' + request.lastName);
            return request;
        },

        // إحصائيات البيانات
        getStats: function() {
            const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
            const licenses = JSON.parse(localStorage.getItem('adminLicenses') || '[]');
            const clients = JSON.parse(localStorage.getItem('adminClients') || '[]');

            return {
                totalRequests: requests.length,
                pendingRequests: requests.filter(r => r.status === 'pending').length,
                approvedRequests: requests.filter(r => r.status === 'approved').length,
                rejectedRequests: requests.filter(r => r.status === 'rejected').length,
                totalLicenses: licenses.length,
                activeLicenses: licenses.filter(l => l.status === 'active').length,
                totalClients: clients.length
            };
        },

        // تصدير البيانات
        exportData: function() {
            const data = {
                activationRequests: JSON.parse(localStorage.getItem('activationRequests') || '[]'),
                licenses: JSON.parse(localStorage.getItem('adminLicenses') || '[]'),
                clients: JSON.parse(localStorage.getItem('adminClients') || '[]'),
                exportDate: new Date().toISOString(),
                version: '2.2.0'
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `activation-data-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);

            console.log('✅ تم تصدير البيانات');
            return true;
        }
    }
};

// تصدير البيانات للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DemoData;
} else if (typeof window !== 'undefined') {
    window.DemoData = DemoData;
}

// تحميل البيانات التجريبية تلقائياً إذا لم توجد بيانات
document.addEventListener('DOMContentLoaded', function() {
    const existingRequests = localStorage.getItem('activationRequests');
    if (!existingRequests || JSON.parse(existingRequests).length === 0) {
        console.log('📊 لا توجد بيانات محفوظة، سيتم تحميل البيانات التجريبية...');
        DemoData.utils.loadDemoData();
    }
});
