// التطبيق الرئيسي للوحة التحكم
class DashboardApp {
    constructor() {
        this.isInitialized = false;
        this.currentSection = 'dashboard';
        this.sidebarCollapsed = false;
        this.notifications = [];
        this.charts = {};
        
        this.init();
    }

    // تهيئة التطبيق
    async init() {
        try {
            console.log('🚀 بدء تهيئة التطبيق...');
            
            // إظهار شاشة التحميل
            this.showLoadingScreen();
            
            // انتظار تحميل الأنظمة الأساسية
            await this.waitForCoreSystems();
            
            // فحص المصادقة
            if (!this.checkAuthentication()) {
                this.redirectToLogin();
                return;
            }
            
            // تهيئة المكونات
            await this.initializeComponents();
            
            // تحميل البيانات
            await this.loadInitialData();
            
            // إعداد الأحداث
            this.setupEventListeners();
            
            // إخفاء شاشة التحميل وإظهار التطبيق
            this.hideLoadingScreen();
            
            this.isInitialized = true;
            console.log('✅ تم تهيئة التطبيق بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة التطبيق:', error);
            this.showError('فشل في تحميل التطبيق. يرجى إعادة تحميل الصفحة.');
        }
    }

    // انتظار تحميل الأنظمة الأساسية
    async waitForCoreSystems() {
        const maxWait = 10000; // 10 ثوان
        const startTime = Date.now();
        
        while (Date.now() - startTime < maxWait) {
            if (window.AuthManager && window.DataManager && window.AppRouter) {
                // انتظار تهيئة الأنظمة
                await new Promise(resolve => setTimeout(resolve, 500));
                return;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        throw new Error('فشل في تحميل الأنظمة الأساسية');
    }

    // فحص المصادقة
    checkAuthentication() {
        if (!window.AuthManager.isLoggedIn()) {
            console.log('🔒 المستخدم غير مسجل الدخول');
            return false;
        }
        
        // فحص صلاحية الترخيص
        const license = window.AuthManager.getCurrentLicense();
        if (!license || new Date(license.expiryDate) <= new Date()) {
            console.log('🔑 الترخيص منتهي الصلاحية');
            this.showLicenseExpiredDialog();
            return false;
        }
        
        return true;
    }

    // إعادة توجيه لصفحة تسجيل الدخول
    redirectToLogin() {
        window.location.href = 'src/auth/login.html';
    }

    // تهيئة المكونات
    async initializeComponents() {
        // تحديث معلومات المستخدم
        this.updateUserInfo();
        
        // تحديث معلومات الترخيص
        this.updateLicenseInfo();
        
        // إعداد الصلاحيات
        this.setupPermissions();
        
        // تهيئة الرسوم البيانية
        this.initializeCharts();
        
        // تحميل الإشعارات
        await this.loadNotifications();
    }

    // تحديث معلومات المستخدم
    updateUserInfo() {
        const user = window.AuthManager.getCurrentUser();
        if (user) {
            document.getElementById('user-name').textContent = user.name || user.username;
            document.getElementById('user-role').textContent = this.getRoleDisplayName(user.role);
        }
    }

    // تحديث معلومات الترخيص
    updateLicenseInfo() {
        const license = window.AuthManager.getCurrentLicense();
        if (license) {
            const expiryDate = new Date(license.expiryDate);
            const today = new Date();
            const daysRemaining = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
            
            document.getElementById('days-remaining').textContent = daysRemaining;
            
            const statusElement = document.getElementById('license-status');
            if (daysRemaining <= 7) {
                statusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i><span>الترخيص ينتهي قريباً</span>';
                statusElement.style.color = 'var(--warning-color)';
            } else if (daysRemaining <= 0) {
                statusElement.innerHTML = '<i class="fas fa-times-circle"></i><span>الترخيص منتهي</span>';
                statusElement.style.color = 'var(--danger-color)';
            }
        }
    }

    // إعداد الصلاحيات
    setupPermissions() {
        const user = window.AuthManager.getCurrentUser();
        if (user && (user.role === 'admin' || user.role === 'manager')) {
            // إظهار عناصر الإدارة
            document.querySelectorAll('.admin-only').forEach(element => {
                element.style.display = 'block';
            });
        }
    }

    // تحميل البيانات الأولية
    async loadInitialData() {
        try {
            // تحميل الإحصائيات
            await this.loadStatistics();
            
            // تحميل المواعيد القادمة
            await this.loadUpcomingAppointments();
            
            // تحميل البطاقات المنتهية الصلاحية
            await this.loadExpiringCards();
            
            // تحديث الرسوم البيانية
            this.updateCharts();
            
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات:', error);
        }
    }

    // تحميل الإحصائيات
    async loadStatistics() {
        try {
            const customers = await window.DataManager.getAll('customers');
            const vehicles = await window.DataManager.getAll('vehicles');
            const gasCards = await window.DataManager.getAll('gasCards');
            const appointments = await window.DataManager.getAll('appointments');
            
            // تحديث العدادات
            document.getElementById('total-customers').textContent = customers.length;
            document.getElementById('total-vehicles').textContent = vehicles.length;
            
            // حساب البطاقات النشطة
            const activeCards = gasCards.filter(card => card.status === 'active').length;
            document.getElementById('active-cards').textContent = activeCards;
            
            // حساب مواعيد اليوم
            const today = new Date().toISOString().split('T')[0];
            const todayAppointments = appointments.filter(apt => 
                apt.date && apt.date.startsWith(today)
            ).length;
            document.getElementById('today-appointments').textContent = todayAppointments;
            
        } catch (error) {
            console.error('❌ خطأ في تحميل الإحصائيات:', error);
        }
    }

    // تحميل المواعيد القادمة
    async loadUpcomingAppointments() {
        try {
            const appointments = await window.DataManager.getAll('appointments');
            const customers = await window.DataManager.getAll('customers');
            
            // فلترة المواعيد القادمة (الأسبوع القادم)
            const nextWeek = new Date();
            nextWeek.setDate(nextWeek.getDate() + 7);
            
            const upcomingAppointments = appointments
                .filter(apt => {
                    const aptDate = new Date(apt.date);
                    return aptDate >= new Date() && aptDate <= nextWeek;
                })
                .sort((a, b) => new Date(a.date) - new Date(b.date))
                .slice(0, 5);
            
            // تحديث الجدول
            const tbody = document.querySelector('#upcoming-appointments tbody');
            tbody.innerHTML = '';
            
            upcomingAppointments.forEach(apt => {
                const customer = customers.find(c => c.id === apt.customerId);
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${customer ? customer.name : 'غير محدد'}</td>
                    <td>${this.formatDate(apt.date)}</td>
                    <td>${apt.time || '--'}</td>
                    <td>${apt.type || 'عام'}</td>
                `;
                tbody.appendChild(row);
            });
            
            if (upcomingAppointments.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" style="text-align: center;">لا توجد مواعيد قادمة</td></tr>';
            }
            
        } catch (error) {
            console.error('❌ خطأ في تحميل المواعيد:', error);
        }
    }

    // تحميل البطاقات المنتهية الصلاحية
    async loadExpiringCards() {
        try {
            const gasCards = await window.DataManager.getAll('gasCards');
            const customers = await window.DataManager.getAll('customers');
            
            // فلترة البطاقات المنتهية الصلاحية (الشهر القادم)
            const nextMonth = new Date();
            nextMonth.setMonth(nextMonth.getMonth() + 1);
            
            const expiringCards = gasCards
                .filter(card => {
                    if (!card.expiryDate) return false;
                    const expiryDate = new Date(card.expiryDate);
                    return expiryDate >= new Date() && expiryDate <= nextMonth;
                })
                .sort((a, b) => new Date(a.expiryDate) - new Date(b.expiryDate))
                .slice(0, 5);
            
            // تحديث الجدول
            const tbody = document.querySelector('#expiring-cards tbody');
            tbody.innerHTML = '';
            
            expiringCards.forEach(card => {
                const customer = customers.find(c => c.id === card.customerId);
                const daysUntilExpiry = Math.ceil((new Date(card.expiryDate) - new Date()) / (1000 * 60 * 60 * 24));
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${card.cardNumber}</td>
                    <td>${customer ? customer.name : 'غير محدد'}</td>
                    <td>${this.formatDate(card.expiryDate)}</td>
                    <td>
                        <span class="status-badge ${daysUntilExpiry <= 7 ? 'danger' : 'warning'}">
                            ${daysUntilExpiry} يوم
                        </span>
                    </td>
                `;
                tbody.appendChild(row);
            });
            
            if (expiringCards.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" style="text-align: center;">لا توجد بطاقات منتهية الصلاحية</td></tr>';
            }
            
        } catch (error) {
            console.error('❌ خطأ في تحميل البطاقات المنتهية:', error);
        }
    }

    // تهيئة الرسوم البيانية
    initializeCharts() {
        try {
            const ctx = document.getElementById('sales-chart');
            if (ctx) {
                this.charts.sales = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                        datasets: [{
                            label: 'المبيعات',
                            data: [12, 19, 3, 5, 2, 3],
                            borderColor: 'rgb(33, 150, 243)',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        } catch (error) {
            console.error('❌ خطأ في تهيئة الرسوم البيانية:', error);
        }
    }

    // تحديث الرسوم البيانية
    updateCharts() {
        // سيتم تحديث البيانات من قاعدة البيانات
        if (this.charts.sales) {
            // تحديث بيانات المبيعات
            this.charts.sales.data.datasets[0].data = [15, 25, 18, 30, 22, 35];
            this.charts.sales.update();
        }
    }

    // تحميل الإشعارات
    async loadNotifications() {
        try {
            // تحميل الإشعارات من قاعدة البيانات
            this.notifications = [
                {
                    id: 1,
                    title: 'موعد جديد',
                    message: 'تم حجز موعد جديد لعميل أحمد محمد',
                    type: 'info',
                    time: new Date(),
                    read: false
                },
                {
                    id: 2,
                    title: 'بطاقة منتهية الصلاحية',
                    message: 'بطاقة رقم 12345 ستنتهي خلال 3 أيام',
                    type: 'warning',
                    time: new Date(),
                    read: false
                }
            ];
            
            this.updateNotificationBadge();
            
        } catch (error) {
            console.error('❌ خطأ في تحميل الإشعارات:', error);
        }
    }

    // تحديث شارة الإشعارات
    updateNotificationBadge() {
        const unreadCount = this.notifications.filter(n => !n.read).length;
        const badge = document.getElementById('notification-count');
        if (badge) {
            badge.textContent = unreadCount;
            badge.style.display = unreadCount > 0 ? 'block' : 'none';
        }
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // تبديل الشريط الجانبي
        document.getElementById('sidebar-toggle')?.addEventListener('click', () => {
            this.toggleSidebar();
        });
        
        // التنقل في الشريط الجانبي
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.dataset.section;
                if (section) {
                    this.switchSection(section);
                }
            });
        });
        
        // الإجراءات السريعة
        document.querySelectorAll('.quick-action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const route = btn.dataset.route;
                if (route) {
                    this.switchSection(route.substring(1));
                }
            });
        });
        
        // تسجيل الخروج
        document.getElementById('logout-btn')?.addEventListener('click', (e) => {
            e.preventDefault();
            this.logout();
        });
        
        // تحديث لوحة التحكم
        document.getElementById('refresh-dashboard')?.addEventListener('click', () => {
            this.refreshDashboard();
        });
        
        // البحث العام
        document.getElementById('global-search')?.addEventListener('input', (e) => {
            this.handleGlobalSearch(e.target.value);
        });
    }

    // تبديل الشريط الجانبي
    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('collapsed');
        this.sidebarCollapsed = !this.sidebarCollapsed;
    }

    // تبديل القسم
    switchSection(sectionName) {
        // إخفاء جميع الأقسام
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        
        // إظهار القسم المطلوب
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
        }
        
        // تحديث التنقل
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = document.querySelector(`[data-section="${sectionName}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
        
        this.currentSection = sectionName;
        
        // تحميل محتوى القسم إذا لزم الأمر
        this.loadSectionContent(sectionName);
    }

    // تحميل محتوى القسم
    async loadSectionContent(sectionName) {
        // سيتم تنفيذ هذا لاحقاً لتحميل المحتوى الديناميكي
        console.log(`تحميل محتوى القسم: ${sectionName}`);
    }

    // تسجيل الخروج
    async logout() {
        if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
            await window.AuthManager.logout();
        }
    }

    // تحديث لوحة التحكم
    async refreshDashboard() {
        try {
            await this.loadInitialData();
            this.showSuccess('تم تحديث البيانات بنجاح');
        } catch (error) {
            this.showError('فشل في تحديث البيانات');
        }
    }

    // البحث العام
    handleGlobalSearch(query) {
        // سيتم تنفيذ البحث العام لاحقاً
        console.log(`البحث عن: ${query}`);
    }

    // إظهار شاشة التحميل
    showLoadingScreen() {
        document.getElementById('loading-screen').style.display = 'flex';
        document.getElementById('app-container').style.display = 'none';
    }

    // إخفاء شاشة التحميل
    hideLoadingScreen() {
        document.getElementById('loading-screen').style.display = 'none';
        document.getElementById('app-container').style.display = 'block';
    }

    // عرض رسالة نجاح
    showSuccess(message) {
        this.showToast(message, 'success');
    }

    // عرض رسالة خطأ
    showError(message) {
        this.showToast(message, 'error');
    }

    // عرض إشعار
    showToast(message, type = 'info') {
        // سيتم تنفيذ نظام الإشعارات لاحقاً
        console.log(`${type.toUpperCase()}: ${message}`);
    }

    // عرض حوار انتهاء الترخيص
    showLicenseExpiredDialog() {
        alert('انتهت صلاحية الترخيص. يرجى تجديد الترخيص للمتابعة.');
        window.location.href = 'activate-license.html';
    }

    // تنسيق التاريخ
    formatDate(dateString) {
        if (!dateString) return '--';
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA');
    }

    // الحصول على اسم الدور
    getRoleDisplayName(role) {
        const roleNames = {
            admin: 'مدير عام',
            manager: 'مدير',
            user: 'مستخدم',
            employee: 'موظف'
        };
        return roleNames[role] || role;
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.DashboardApp = new DashboardApp();
});

// تصدير للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DashboardApp;
}
