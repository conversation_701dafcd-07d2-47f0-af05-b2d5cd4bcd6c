/* أنماط صفحة تسجيل الدخول */

/* المتغيرات */
:root {
    --primary-color: #2196F3;
    --secondary-color: #FFC107;
    --success-color: #4CAF50;
    --danger-color: #F44336;
    --warning-color: #FF9800;
    --info-color: #00BCD4;
    --dark-color: #212529;
    --light-color: #F8F9FA;
    --border-color: #DEE2E6;
    --text-color: #495057;
    --text-muted: #6C757D;
    --bg-color: #FFFFFF;
    --shadow: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 8px 25px rgba(0,0,0,0.15);
    --border-radius: 12px;
    --transition: all 0.3s ease;
    --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* إعادة تعيين الأنماط */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    direction: rtl;
    overflow: hidden;
}

/* الخلفية المتحركة */
.animated-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.floating-shapes {
    position: relative;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 15%;
    animation-delay: 1s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 2s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    bottom: 30%;
    right: 10%;
    animation-delay: 3s;
}

.shape-5 {
    width: 140px;
    height: 140px;
    top: 50%;
    left: 50%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* الحاوي الرئيسي */
.login-container {
    display: flex;
    width: 90%;
    max-width: 1200px;
    height: 80vh;
    background: var(--bg-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    position: relative;
    z-index: 1;
}

/* الجانب الأيسر - معلومات التطبيق */
.login-info {
    flex: 1;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.login-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.info-content {
    text-align: center;
    position: relative;
    z-index: 1;
}

.app-logo {
    margin-bottom: 2rem;
}

.app-logo img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.info-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.info-content h2 {
    font-size: 1.5rem;
    font-weight: 400;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.info-content p {
    font-size: 1.1rem;
    margin-bottom: 3rem;
    opacity: 0.8;
    line-height: 1.6;
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1rem;
    opacity: 0.9;
}

.feature-item i {
    font-size: 1.5rem;
    width: 30px;
    text-align: center;
}

/* الجانب الأيمن - نموذج تسجيل الدخول */
.login-form-container {
    flex: 1;
    padding: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-color);
}

.login-form {
    width: 100%;
    max-width: 400px;
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.form-header h3 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.form-header p {
    color: var(--text-muted);
    font-size: 1rem;
}

/* رسائل التنبيه */
.alert-container {
    margin-bottom: 1.5rem;
}

.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.alert.success {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.alert.error {
    background: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.alert.warning {
    background: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(255, 152, 0, 0.3);
}

/* النموذج */
.form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--dark-color);
    font-size: 0.9rem;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    right: 1rem;
    color: var(--text-muted);
    z-index: 1;
}

.input-group input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--bg-color);
    color: var(--text-color);
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.password-toggle {
    position: absolute;
    left: 1rem;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.5rem;
    z-index: 1;
}

.password-toggle:hover {
    color: var(--primary-color);
}

/* خيارات النموذج */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1rem 0;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-color);
}

.checkbox-container input {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: var(--transition);
}

.checkbox-container input:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-container input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.forgot-password:hover {
    text-decoration: underline;
}

/* زر تسجيل الدخول */
.login-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, var(--primary-color), #1976D2);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* بيانات تسجيل الدخول التجريبية */
.demo-credentials {
    margin-top: 2rem;
    padding: 1.5rem;
    background: var(--light-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.demo-credentials h4 {
    color: var(--dark-color);
    margin-bottom: 1rem;
    font-size: 0.9rem;
    text-align: center;
}

.credentials-grid {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.credential-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--bg-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.85rem;
}

.credential-item:hover {
    border-color: var(--primary-color);
    background: rgba(33, 150, 243, 0.05);
}

.credential-item strong {
    color: var(--dark-color);
}

.credential-item span {
    color: var(--text-muted);
    font-family: monospace;
}

/* الروابط الإضافية */
.additional-links {
    margin-top: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
    padding: 0.5rem;
    border-radius: 8px;
}

.link:hover {
    background: rgba(33, 150, 243, 0.1);
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: var(--bg-color);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    color: var(--dark-color);
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--light-color);
    color: var(--dark-color);
}

.modal-body {
    padding: 1.5rem;
}

.info-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--light-color);
    border-radius: 8px;
}

.info-item strong {
    color: var(--dark-color);
}

.info-item span {
    color: var(--text-muted);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--light-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.contact-item i {
    color: var(--primary-color);
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .login-container {
        flex-direction: column;
        width: 95%;
        height: 95vh;
    }
    
    .login-info {
        flex: 0 0 auto;
        padding: 2rem 1.5rem;
    }
    
    .info-content h1 {
        font-size: 1.75rem;
    }
    
    .info-content h2 {
        font-size: 1.25rem;
    }
    
    .features-list {
        display: none;
    }
    
    .login-form-container {
        padding: 2rem 1.5rem;
    }
    
    .demo-credentials {
        margin-top: 1.5rem;
        padding: 1rem;
    }
    
    .credentials-grid {
        gap: 0.5rem;
    }
    
    .credential-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}
