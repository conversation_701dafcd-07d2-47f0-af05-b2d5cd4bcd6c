// نظام إدارة البيانات المركزي
class DataManager {
    constructor() {
        this.dbName = 'FutureFuelDB';
        this.version = 1;
        this.db = null;
        this.stores = {
            customers: 'customers',
            vehicles: 'vehicles',
            gasCards: 'gasCards',
            appointments: 'appointments',
            licenses: 'licenses',
            activationRequests: 'activationRequests',
            certificates: 'certificates',
            inventory: 'inventory',
            sales: 'sales',
            purchases: 'purchases',
            debts: 'debts',
            suppliers: 'suppliers',
            settings: 'settings',
            backups: 'backups'
        };
        
        this.init();
    }

    // تهيئة قاعدة البيانات
    async init() {
        try {
            await this.openDatabase();
            await this.loadInitialData();
            console.log('✅ تم تهيئة نظام إدارة البيانات بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام إدارة البيانات:', error);
            // استخدام localStorage كبديل
            this.useLocalStorageFallback();
        }
    }

    // فتح قاعدة البيانات
    openDatabase() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);

            request.onerror = () => {
                reject(new Error('فشل في فتح قاعدة البيانات'));
            };

            request.onsuccess = (event) => {
                this.db = event.target.result;
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                this.createStores(db);
            };
        });
    }

    // إنشاء المخازن
    createStores(db) {
        // مخزن العملاء
        if (!db.objectStoreNames.contains(this.stores.customers)) {
            const customerStore = db.createObjectStore(this.stores.customers, { keyPath: 'id', autoIncrement: true });
            customerStore.createIndex('name', 'name', { unique: false });
            customerStore.createIndex('phone', 'phone', { unique: true });
        }

        // مخزن المركبات
        if (!db.objectStoreNames.contains(this.stores.vehicles)) {
            const vehicleStore = db.createObjectStore(this.stores.vehicles, { keyPath: 'id', autoIncrement: true });
            vehicleStore.createIndex('plateNumber', 'plateNumber', { unique: true });
            vehicleStore.createIndex('customerId', 'customerId', { unique: false });
        }

        // مخزن بطاقات الغاز
        if (!db.objectStoreNames.contains(this.stores.gasCards)) {
            const gasCardStore = db.createObjectStore(this.stores.gasCards, { keyPath: 'id', autoIncrement: true });
            gasCardStore.createIndex('cardNumber', 'cardNumber', { unique: true });
            gasCardStore.createIndex('vehicleId', 'vehicleId', { unique: false });
        }

        // مخزن المواعيد
        if (!db.objectStoreNames.contains(this.stores.appointments)) {
            const appointmentStore = db.createObjectStore(this.stores.appointments, { keyPath: 'id', autoIncrement: true });
            appointmentStore.createIndex('date', 'date', { unique: false });
            appointmentStore.createIndex('customerId', 'customerId', { unique: false });
        }

        // مخزن التراخيص
        if (!db.objectStoreNames.contains(this.stores.licenses)) {
            const licenseStore = db.createObjectStore(this.stores.licenses, { keyPath: 'id', autoIncrement: true });
            licenseStore.createIndex('key', 'key', { unique: true });
            licenseStore.createIndex('status', 'status', { unique: false });
        }

        // مخزن طلبات التفعيل
        if (!db.objectStoreNames.contains(this.stores.activationRequests)) {
            const requestStore = db.createObjectStore(this.stores.activationRequests, { keyPath: 'id', autoIncrement: true });
            requestStore.createIndex('status', 'status', { unique: false });
            requestStore.createIndex('requestDate', 'requestDate', { unique: false });
        }

        // مخزن الشهادات
        if (!db.objectStoreNames.contains(this.stores.certificates)) {
            const certStore = db.createObjectStore(this.stores.certificates, { keyPath: 'id', autoIncrement: true });
            certStore.createIndex('type', 'type', { unique: false });
            certStore.createIndex('vehicleId', 'vehicleId', { unique: false });
        }

        // باقي المخازن
        Object.values(this.stores).forEach(storeName => {
            if (!db.objectStoreNames.contains(storeName)) {
                db.createObjectStore(storeName, { keyPath: 'id', autoIncrement: true });
            }
        });
    }

    // إضافة عنصر
    async add(storeName, data) {
        try {
            if (!this.db) {
                return this.addToLocalStorage(storeName, data);
            }

            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            
            // إضافة timestamp
            data.createdAt = new Date().toISOString();
            data.updatedAt = new Date().toISOString();
            
            const request = store.add(data);
            
            return new Promise((resolve, reject) => {
                request.onsuccess = () => {
                    console.log(`✅ تم إضافة عنصر جديد في ${storeName}`);
                    resolve(request.result);
                };
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error(`❌ خطأ في إضافة عنصر في ${storeName}:`, error);
            throw error;
        }
    }

    // تحديث عنصر
    async update(storeName, data) {
        try {
            if (!this.db) {
                return this.updateInLocalStorage(storeName, data);
            }

            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            
            // تحديث timestamp
            data.updatedAt = new Date().toISOString();
            
            const request = store.put(data);
            
            return new Promise((resolve, reject) => {
                request.onsuccess = () => {
                    console.log(`✅ تم تحديث عنصر في ${storeName}`);
                    resolve(request.result);
                };
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error(`❌ خطأ في تحديث عنصر في ${storeName}:`, error);
            throw error;
        }
    }

    // حذف عنصر
    async delete(storeName, id) {
        try {
            if (!this.db) {
                return this.deleteFromLocalStorage(storeName, id);
            }

            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.delete(id);
            
            return new Promise((resolve, reject) => {
                request.onsuccess = () => {
                    console.log(`✅ تم حذف عنصر من ${storeName}`);
                    resolve(true);
                };
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error(`❌ خطأ في حذف عنصر من ${storeName}:`, error);
            throw error;
        }
    }

    // الحصول على عنصر بالمعرف
    async get(storeName, id) {
        try {
            if (!this.db) {
                return this.getFromLocalStorage(storeName, id);
            }

            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(id);
            
            return new Promise((resolve, reject) => {
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error(`❌ خطأ في الحصول على عنصر من ${storeName}:`, error);
            throw error;
        }
    }

    // الحصول على جميع العناصر
    async getAll(storeName) {
        try {
            if (!this.db) {
                return this.getAllFromLocalStorage(storeName);
            }

            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();
            
            return new Promise((resolve, reject) => {
                request.onsuccess = () => resolve(request.result || []);
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error(`❌ خطأ في الحصول على جميع العناصر من ${storeName}:`, error);
            return [];
        }
    }

    // البحث بالفهرس
    async getByIndex(storeName, indexName, value) {
        try {
            if (!this.db) {
                return this.getByIndexFromLocalStorage(storeName, indexName, value);
            }

            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const index = store.index(indexName);
            const request = index.getAll(value);
            
            return new Promise((resolve, reject) => {
                request.onsuccess = () => resolve(request.result || []);
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error(`❌ خطأ في البحث بالفهرس في ${storeName}:`, error);
            return [];
        }
    }

    // استخدام localStorage كبديل
    useLocalStorageFallback() {
        console.warn('⚠️ استخدام localStorage كبديل لقاعدة البيانات');
        this.db = null;
    }

    // دوال localStorage البديلة
    addToLocalStorage(storeName, data) {
        const items = JSON.parse(localStorage.getItem(storeName) || '[]');
        data.id = Date.now() + Math.random();
        data.createdAt = new Date().toISOString();
        data.updatedAt = new Date().toISOString();
        items.push(data);
        localStorage.setItem(storeName, JSON.stringify(items));
        return data.id;
    }

    updateInLocalStorage(storeName, data) {
        const items = JSON.parse(localStorage.getItem(storeName) || '[]');
        const index = items.findIndex(item => item.id === data.id);
        if (index !== -1) {
            data.updatedAt = new Date().toISOString();
            items[index] = data;
            localStorage.setItem(storeName, JSON.stringify(items));
        }
        return data.id;
    }

    deleteFromLocalStorage(storeName, id) {
        const items = JSON.parse(localStorage.getItem(storeName) || '[]');
        const filteredItems = items.filter(item => item.id !== id);
        localStorage.setItem(storeName, JSON.stringify(filteredItems));
        return true;
    }

    getFromLocalStorage(storeName, id) {
        const items = JSON.parse(localStorage.getItem(storeName) || '[]');
        return items.find(item => item.id === id);
    }

    getAllFromLocalStorage(storeName) {
        return JSON.parse(localStorage.getItem(storeName) || '[]');
    }

    getByIndexFromLocalStorage(storeName, indexName, value) {
        const items = JSON.parse(localStorage.getItem(storeName) || '[]');
        return items.filter(item => item[indexName] === value);
    }

    // تحميل البيانات الأولية
    async loadInitialData() {
        try {
            // تحميل الإعدادات الافتراضية
            const settings = await this.getAll(this.stores.settings);
            if (settings.length === 0) {
                await this.add(this.stores.settings, {
                    key: 'app_config',
                    value: {
                        shopName: 'مؤسسة وقود المستقبل',
                        language: 'ar',
                        theme: 'light',
                        reminderDays: 30,
                        workingHoursStart: '08:00',
                        workingHoursEnd: '18:00'
                    }
                });
            }

            console.log('✅ تم تحميل البيانات الأولية');
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات الأولية:', error);
        }
    }

    // إنشاء نسخة احتياطية
    async createBackup() {
        try {
            const backup = {
                timestamp: new Date().toISOString(),
                version: this.version,
                data: {}
            };

            // جمع البيانات من جميع المخازن
            for (const [key, storeName] of Object.entries(this.stores)) {
                backup.data[key] = await this.getAll(storeName);
            }

            // حفظ النسخة الاحتياطية
            await this.add(this.stores.backups, backup);

            console.log('✅ تم إنشاء نسخة احتياطية');
            return backup;
        } catch (error) {
            console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);
            throw error;
        }
    }

    // استعادة من نسخة احتياطية
    async restoreBackup(backupId) {
        try {
            const backup = await this.get(this.stores.backups, backupId);
            if (!backup) {
                throw new Error('النسخة الاحتياطية غير موجودة');
            }

            // استعادة البيانات
            for (const [key, data] of Object.entries(backup.data)) {
                const storeName = this.stores[key];
                if (storeName && Array.isArray(data)) {
                    // مسح البيانات الحالية
                    const currentData = await this.getAll(storeName);
                    for (const item of currentData) {
                        await this.delete(storeName, item.id);
                    }

                    // إضافة البيانات المستعادة
                    for (const item of data) {
                        await this.add(storeName, item);
                    }
                }
            }

            console.log('✅ تم استعادة النسخة الاحتياطية');
            return true;
        } catch (error) {
            console.error('❌ خطأ في استعادة النسخة الاحتياطية:', error);
            throw error;
        }
    }

    // تصدير البيانات
    async exportData() {
        try {
            const exportData = {
                timestamp: new Date().toISOString(),
                version: this.version,
                data: {}
            };

            for (const [key, storeName] of Object.entries(this.stores)) {
                exportData.data[key] = await this.getAll(storeName);
            }

            return exportData;
        } catch (error) {
            console.error('❌ خطأ في تصدير البيانات:', error);
            throw error;
        }
    }
}

// إنشاء مثيل عام لمدير البيانات
window.DataManager = new DataManager();

// تصدير للاستخدام في Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataManager;
}
