// نظام التوجيه المركزي للتطبيق
class AppRouter {
    constructor() {
        this.routes = new Map();
        this.currentRoute = null;
        this.history = [];
        this.middlewares = [];
        this.isAuthenticated = false;
        this.userRole = null;
        
        this.init();
    }

    init() {
        this.setupRoutes();
        this.setupEventListeners();
        this.checkAuthentication();
        this.handleInitialRoute();
    }

    // إعداد المسارات
    setupRoutes() {
        // المسارات العامة
        this.addRoute('/', {
            component: 'dashboard',
            title: 'لوحة التحكم الرئيسية',
            requireAuth: true,
            file: 'index.html'
        });

        this.addRoute('/login', {
            component: 'login',
            title: 'تسجيل الدخول',
            requireAuth: false,
            file: 'src/auth/login.html'
        });

        this.addRoute('/activate', {
            component: 'activate',
            title: 'تفعيل الترخيص',
            requireAuth: false,
            file: 'activate-license.html'
        });

        // مسارات لوحة التحكم
        this.addRoute('/admin', {
            component: 'admin-panel',
            title: 'لوحة التحكم الإدارية',
            requireAuth: true,
            requireRole: ['admin', 'manager'],
            file: 'src/remote/admin-panel.html'
        });

        this.addRoute('/admin/requests', {
            component: 'activation-requests',
            title: 'طلبات التفعيل',
            requireAuth: true,
            requireRole: ['admin', 'manager'],
            file: 'src/remote/admin-panel.html',
            section: 'activations'
        });

        this.addRoute('/admin/licenses', {
            component: 'licenses',
            title: 'إدارة التراخيص',
            requireAuth: true,
            requireRole: ['admin', 'manager'],
            file: 'src/remote/admin-panel.html',
            section: 'licenses'
        });

        this.addRoute('/admin/clients', {
            component: 'clients',
            title: 'إدارة العملاء',
            requireAuth: true,
            requireRole: ['admin', 'manager'],
            file: 'src/remote/admin-panel.html',
            section: 'clients'
        });

        // مسارات التطبيق الرئيسي
        this.addRoute('/customers', {
            component: 'customers',
            title: 'إدارة الزبائن',
            requireAuth: true,
            file: 'index.html',
            section: 'customers'
        });

        this.addRoute('/vehicles', {
            component: 'vehicles',
            title: 'إدارة المركبات',
            requireAuth: true,
            file: 'index.html',
            section: 'vehicles'
        });

        this.addRoute('/certificates', {
            component: 'certificates',
            title: 'الشهادات',
            requireAuth: true,
            file: 'index.html',
            section: 'certificates'
        });

        this.addRoute('/reports', {
            component: 'reports',
            title: 'التقارير',
            requireAuth: true,
            file: 'index.html',
            section: 'reports'
        });
    }

    // إضافة مسار جديد
    addRoute(path, config) {
        this.routes.set(path, config);
    }

    // إضافة middleware
    addMiddleware(middleware) {
        this.middlewares.push(middleware);
    }

    // التنقل إلى مسار
    async navigate(path, options = {}) {
        try {
            console.log(`🧭 التنقل إلى: ${path}`);

            const route = this.routes.get(path);
            if (!route) {
                console.error(`❌ المسار غير موجود: ${path}`);
                this.navigate('/404');
                return false;
            }

            // تشغيل middlewares
            for (const middleware of this.middlewares) {
                const result = await middleware(path, route);
                if (result === false) {
                    console.log('🚫 تم منع التنقل بواسطة middleware');
                    return false;
                }
            }

            // فحص المصادقة
            if (route.requireAuth && !this.isAuthenticated) {
                console.log('🔒 المسار يتطلب مصادقة، إعادة توجيه لتسجيل الدخول');
                this.navigate('/login');
                return false;
            }

            // فحص الصلاحيات
            if (route.requireRole && !this.hasRole(route.requireRole)) {
                console.log('🚫 المستخدم لا يملك الصلاحيات المطلوبة');
                this.showError('ليس لديك صلاحية للوصول إلى هذه الصفحة');
                return false;
            }

            // حفظ المسار الحالي في التاريخ
            if (this.currentRoute) {
                this.history.push(this.currentRoute);
            }

            // تحديث المسار الحالي
            this.currentRoute = path;

            // تحديث عنوان الصفحة
            document.title = route.title || 'مؤسسة وقود المستقبل';

            // تحديث URL في المتصفح
            if (!options.silent) {
                window.history.pushState({ path }, route.title, path);
            }

            // تحميل المكون
            await this.loadComponent(route);

            // إطلاق حدث التنقل
            this.emit('navigate', { path, route });

            return true;

        } catch (error) {
            console.error('❌ خطأ في التنقل:', error);
            this.showError('حدث خطأ أثناء التنقل');
            return false;
        }
    }

    // تحميل المكون
    async loadComponent(route) {
        try {
            if (route.file) {
                // إذا كان المسار يتطلب تحميل ملف جديد
                if (route.file !== this.getCurrentFile()) {
                    window.location.href = route.file;
                    return;
                }
            }

            // إذا كان المسار يتطلب عرض قسم معين
            if (route.section) {
                this.showSection(route.section);
            }

            // تشغيل دالة المكون إذا كانت موجودة
            if (route.component && window[route.component]) {
                await window[route.component].init();
            }

        } catch (error) {
            console.error('❌ خطأ في تحميل المكون:', error);
        }
    }

    // عرض قسم معين
    showSection(sectionId) {
        // إخفاء جميع الأقسام
        document.querySelectorAll('.content-section, .tab-content').forEach(section => {
            section.classList.remove('active');
            section.style.display = 'none';
        });

        // إظهار القسم المطلوب
        const targetSection = document.getElementById(`${sectionId}-section`) || 
                             document.getElementById(sectionId);
        
        if (targetSection) {
            targetSection.classList.add('active');
            targetSection.style.display = 'block';
        }

        // تحديث التنقل
        document.querySelectorAll('.nav-link, .tab-btn').forEach(link => {
            link.classList.remove('active');
        });

        const activeLink = document.querySelector(`[data-section="${sectionId}"]`) ||
                          document.querySelector(`[href="#${sectionId}"]`);
        
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }

    // الحصول على الملف الحالي
    getCurrentFile() {
        const path = window.location.pathname;
        return path.split('/').pop() || 'index.html';
    }

    // فحص المصادقة
    checkAuthentication() {
        try {
            // فحص localStorage
            const session = localStorage.getItem('userSession');
            const license = localStorage.getItem('appLicense');

            if (session && license) {
                const sessionData = JSON.parse(session);
                const licenseData = JSON.parse(license);

                // فحص صلاحية الجلسة
                if (sessionData.expiryDate && new Date(sessionData.expiryDate) > new Date()) {
                    this.isAuthenticated = true;
                    this.userRole = sessionData.role || 'user';
                    return true;
                }
            }

            this.isAuthenticated = false;
            this.userRole = null;
            return false;

        } catch (error) {
            console.error('❌ خطأ في فحص المصادقة:', error);
            this.isAuthenticated = false;
            return false;
        }
    }

    // فحص الصلاحيات
    hasRole(requiredRoles) {
        if (!Array.isArray(requiredRoles)) {
            requiredRoles = [requiredRoles];
        }
        return requiredRoles.includes(this.userRole);
    }

    // العودة للخلف
    goBack() {
        if (this.history.length > 0) {
            const previousRoute = this.history.pop();
            this.navigate(previousRoute, { silent: true });
        } else {
            this.navigate('/');
        }
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // التعامل مع أزرار المتصفح
        window.addEventListener('popstate', (event) => {
            if (event.state && event.state.path) {
                this.navigate(event.state.path, { silent: true });
            }
        });

        // التعامل مع الروابط
        document.addEventListener('click', (event) => {
            const link = event.target.closest('a[data-route]');
            if (link) {
                event.preventDefault();
                const route = link.getAttribute('data-route');
                this.navigate(route);
            }
        });
    }

    // التعامل مع المسار الأولي
    handleInitialRoute() {
        const currentPath = window.location.pathname;
        const route = this.routes.get(currentPath);
        
        if (route) {
            this.navigate(currentPath, { silent: true });
        } else {
            // إعادة توجيه افتراضية
            if (this.isAuthenticated) {
                this.navigate('/');
            } else {
                this.navigate('/login');
            }
        }
    }

    // إطلاق الأحداث
    emit(eventName, data) {
        const event = new CustomEvent(`router:${eventName}`, { detail: data });
        window.dispatchEvent(event);
    }

    // عرض رسالة خطأ
    showError(message) {
        console.error(message);
        // يمكن إضافة نظام إشعارات هنا
    }

    // تسجيل الدخول
    login(userData) {
        this.isAuthenticated = true;
        this.userRole = userData.role || 'user';
        
        // حفظ بيانات الجلسة
        const sessionData = {
            username: userData.username,
            role: this.userRole,
            loginTime: new Date().toISOString(),
            expiryDate: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString() // 8 ساعات
        };
        
        localStorage.setItem('userSession', JSON.stringify(sessionData));
        
        // إعادة توجيه للصفحة الرئيسية
        this.navigate('/');
    }

    // تسجيل الخروج
    logout() {
        this.isAuthenticated = false;
        this.userRole = null;
        
        // مسح بيانات الجلسة
        localStorage.removeItem('userSession');
        
        // إعادة توجيه لصفحة تسجيل الدخول
        this.navigate('/login');
    }
}

// إنشاء مثيل عام للموجه
window.AppRouter = new AppRouter();

// تصدير للاستخدام في Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppRouter;
}
